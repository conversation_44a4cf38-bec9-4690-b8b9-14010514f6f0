{"securityGroupIds": ["sg-06e06ee52057fb09f"], "subnetIds": ["subnet-023ff1fb8431b273f", "subnet-09dd9cf2a9239643c"], "dbSecretName": "PROD/CANNY/PGACCESS", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::378423228887:role/lambdaFullAccess", "dbPrefix": "cannyhr_", "domainName": "cannyhr", "customDomainName": "api.cannyhr.com", "firebaseAuthorizer": "arn:aws:lambda:ap-south-1:378423228887:function:ATS-cannyhr-firebaseauthorizer", "profileBucket": "s3.images.cannyhr.com", "documentsBucket": "s3.taxdocs.cannyhr.com", "sesTemplatesRegion": "us-east-1", "sourceEmailAddress": "<EMAIL>", "logoBucket": "s3.logos.cannyhr.com", "webAddress": ".com", "employeeTaxDetailUrl": "https://{orgCode}.hrapp.co.in/payroll/salary-payslip/get-employee-tax-details", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:378423228887:function:PAYMASTER-cannyhr", "processCancelSalaryRevisionsFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-processCancelSalaryRevisionsFunction", "processSalaryImportFunction": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-processSalaryImportFunction", "stateMachineArn": "arn:aws:states:ap-south-1:378423228887:stateMachine:cannyhr-bulkPayslipProcessing", "bucketName": "s3.taxdocs.cannyhr.com", "bulkProcessingLambdaArn": "arn:aws:lambda:ap-south-1:378423228887:function:BULKPROCESSING-cannyhr-graphql", "initiatePayslipRefreshArn": "arn:aws:lambda:ap-south-1:378423228887:function:cannyhr-initiatePayslipRefresh"}