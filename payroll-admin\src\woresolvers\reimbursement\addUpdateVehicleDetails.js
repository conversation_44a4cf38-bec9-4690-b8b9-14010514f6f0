const { validateCommonRuleInput } = require('../../../common/commonValidation');
//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
//Require apollo server to return error message
const { UserInputError,ApolloError } = require('apollo-server-lambda');
//Require table alias
const { ehrTables } = require('../../../common/tableAlias');
//Require moment
const moment = require('moment');


module.exports.addUpdateVehicleDetails = async function (parent, args, context, info) {
    let organizationDbConnection;
    let validationError = {};
    try {
        console.log('Inside addUpdateVehicleDetails function');
        const loggedInEmpId = context.Employee_Id;
        const isEditMode = !!args.vehicleId;

        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // check access right based on employeeid
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            loggedInEmpId,
            null,
            '',
            'UI',
            false,
            args.formId
        );

        if (Object.keys(checkRights).length === 0) {
            throw '_DB0100';
        }

        // Check appropriate rights based on operation
        if (isEditMode) {
            if (checkRights.Role_Update !== 1) {
                throw '_DB0102';
            }
        } else {
            if (checkRights.Role_Add !== 1) {
                throw '_DB0101';
            }
        }

        // Input validation using validateCommonRuleInput
        const fieldValidations = {};

        // Map fields to validation rules
        if (args.vehicleName) fieldValidations.vehicleName = 'IVE0971';
        if (args.vehicleType) fieldValidations.vehicleType = 'IVE0972';
        if (args.engineCapacityCC) fieldValidations.engineCapacityCC = 'IVE0973';

        validationError = await validateCommonRuleInput(args, fieldValidations);

        // Additional custom validations that can't be handled by common validation
        await validateVehicleSpecificRules(args, validationError);

        if (Object.keys(validationError).length > 0) {
            throw 'IVE0000';
        }

        // Process add/update operation
        const result = await processVehicleDetails(organizationDbConnection, args, loggedInEmpId, isEditMode);

        // Create system log activities
        const systemLogParam = {
            organizationDbConnection,
            employeeId: loggedInEmpId,
            action: isEditMode ? 'update' : 'add',
            tableName: ehrTables.vehicleDetails,
            uniqueId: result.vehicleId,
            changedData: JSON.stringify(args),
            formId: args.formId,
        };
        await commonLib.func.createSystemLogActivities(systemLogParam);

        // return response
        return {
            errorCode: '',
            message: isEditMode ? 'Vehicle details updated successfully.' : 'Vehicle details added successfully.',
            success: true,
            vehicleId: result.vehicleId
        };
    }
    catch (mainCatchError) {
        console.log('Error in addUpdateVehicleDetails function main catch block', mainCatchError);
        if (mainCatchError === 'IVE0000') {
            const errResult = commonLib.func.getError('', 'IVE0000');
            throw new UserInputError(errResult.message, { validationError: validationError });
        } else {
            const errResult = commonLib.func.getError(mainCatchError, 'SRE0006');
            throw new ApolloError(errResult.message, errResult.code);
        }
    }
    finally {
        if (organizationDbConnection) {
            await organizationDbConnection.destroy();
        }
    }
};

// function to validate vehicle specific rules that can't be handled by common validation
function validateVehicleSpecificRules(args, validationError) {
    // Validate enum fields
    const validRecordMileages = ['Distance Traveled (Enter Manually)','Odometer Reading','GPS'];
    if (args.recordMileageUsing && !validRecordMileages.includes(args.recordMileageUsing)) {
        validationError['IVE0974'] = commonLib.func.getError('', 'IVE0974').message1;
    }

    const validRecordStatus = ['Active','Inactive'];
    if (args.recordStatus && !validRecordStatus.includes(args.recordStatus)) {
        validationError['IVE0975'] = commonLib.func.getError('', 'IVE0975').message1;
    }

}

// function to process vehicle details add/update
async function processVehicleDetails(organizationDbConnection, args, loggedInEmpId, isEditMode) {
    try {
        return await organizationDbConnection.transaction(async (trx) => {
            const currentTimestamp = moment.utc().format('YYYY-MM-DD HH:mm:ss');

            // Prepare vehicle details data
            const vehicleData = {
                Vehicle_Name: args.vehicleName || null,
                Record_Mileage_Using: args.recordMileageUsing || null,
                Record_Status: args.recordStatus ?? null,
                Vehicle_Type: args.vehicleType ?? null,
                Engine_Capacity_CC: args.engineCapacityCC ?? null,
            };

            // Set audit fields
            if (isEditMode) {
                vehicleData.Updated_By = loggedInEmpId;
                vehicleData.Updated_On = currentTimestamp;
            } else {
                vehicleData.Added_By = loggedInEmpId;
                vehicleData.Added_On = currentTimestamp;
            }

            let vehicleId;

            if (isEditMode) {
                // Update existing vehicle data
                const updatedCount = await organizationDbConnection(ehrTables.vehicleDetails)
                    .where('Vehicle_Id', args.vehicleId)
                    .update(vehicleData)
                    .transacting(trx);

                if (updatedCount === 0) {
                    throw 'SRE0004'; // record not found
                }
                vehicleId = args.vehicleId;
            } else {
                // Insert new vehicle data
                const [insertId] = await organizationDbConnection(ehrTables.vehicleDetails)
                    .insert(vehicleData)
                    .transacting(trx);

                vehicleId = insertId;
            }

            return { vehicleId };
        });
    } catch (error) {
        console.log('Error in processVehicleDetails:', error);
        throw error;
    }
}
