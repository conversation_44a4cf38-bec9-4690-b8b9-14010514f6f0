{"securityGroupIds": ["sg-098a0324de5632c9e", "sg-03b2b753038f67293"], "subnetIds": ["subnet-020dff85cddc0752e", "subnet-0b1a6fbc2c7b782ad"], "dbSecretName": "hrapp-stage", "region": "ap-south-1", "lambdaRole": "arn:aws:iam::692647644057:role/lambdaFullAccess", "dbPrefix": "hrapp_", "domainName": "hrapp", "customDomainName": "", "firebaseAuthorizer": "", "profileBucket": "s3.hrapp-dev-public-asset", "documentsBucket": "caprice-dev-stage", "sesTemplatesRegion": "us-east-1", "sourceEmailAddress": "<EMAIL>", "logoBucket": "s3.hrapp-dev-public-images", "webAddress": "", "employeeTaxDetailUrl": "https://{orgCode}.hrapp.co.in/payroll/salary-payslip/get-employee-tax-details", "resourceArnPrefix": "arn:aws:lambda:ap-south-1:692647644057:function:PAYMASTER-dev", "processCancelSalaryRevisionsFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-processCancelSalaryRevisionsFunction", "processSalaryImportFunction": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-processSalaryImportFunction", "stateMachineArn": "arn:aws:states:ap-south-1:692647644057:stateMachine:dev-bulkPayslipProcessing", "bucketName": "caprice-dev-stage", "bulkProcessingLambdaArn": "arn:aws:lambda:ap-south-1:692647644057:function:BULKPROCESSING-dev-graphql", "initiatePayslipRefreshArn": "arn:aws:lambda:ap-south-1:692647644057:function:dev-initiatePayslipRefresh"}