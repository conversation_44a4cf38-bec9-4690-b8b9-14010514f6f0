const { ehrTables } =require('../common/tablealias');

//Function to get the employee sign-in details 
async function checkAllowUserSignin(emailId,employeeStatus, organizationDbConnection){
    console.log('Inside checkAllowUserSignin', emailId,employeeStatus);
   
    emailId = emailId.toLowerCase();
    return new Promise((resolve,reject)=>{
        let empQuery = organizationDbConnection(ehrTables.empPersonalInfo + ' as EMPI')
        .select('EMPI.Allow_User_Signin', 'EMPI.Enable_Sign_In_With_Mobile_No', 'EMJ.Employee_Id', 'EMJ.Emp_Status', 'EMTM.Member_Status','EMU.Firebase_Uid')
        .innerJoin(ehrTables.empJob + ' as EMJ','EMJ.Employee_Id','EMPI.Employee_Id')
        .leftJoin(ehrTables.empUser + ' as EMU', 'EMU.Employee_Id', 'EMJ.Employee_Id')
        .leftJoin(ehrTables.teamMembers + ' as EMTM','EMTM.Employee_Id','EMJ.Employee_Id')
        .whereRaw('LOWER(EMJ.Emp_Email) = LOWER(?)', [emailId]);
        if(employeeStatus){
            empQuery = empQuery.where('EMJ.Emp_Status',employeeStatus);
        }
        return(
            empQuery.then(employeeData =>{
                console.log('employeeData', employeeData);
                if(employeeData && employeeData.length > 0){
                    resolve(employeeData);
                }else{
                    resolve([]);
                }
            })
        ).catch(checkAllowUserSigninErr=>{
            console.log('Error in checkAllowUserSigninErr() function .catch block',checkAllowUserSigninErr);
            reject(checkAllowUserSigninErr);
        })
    })
};

//Function to get the employee sign-in details 
async function getSignInEmployeeDetails(emailId,organizationDbConnection){
    let employeeDetail = await checkAllowUserSignin(emailId,'Active',organizationDbConnection);
    if(employeeDetail && employeeDetail.length > 0){
        return employeeDetail;
    }else{
        let employeeDetail = await checkAllowUserSignin(emailId,'',organizationDbConnection); 
        if(employeeDetail && employeeDetail.length > 0){
            return employeeDetail;
        }else{
            return [];
        }
    }
}

//Function to update the login details once the user logged in to HRAPP
async function updateUserLoginDetails(organizationDbConnection,userDetails){
    try{
        let employeeId = userDetails['Employee_Id'];

        //Check the employee id exist in the employee user details table or not
        let isEmployeeUserDetailsExist = 
        await organizationDbConnection(ehrTables.empUser)
        .pluck('Employee_Id')
        .where('Employee_Id',employeeId)
        .then((employeeUserDetails) => {
            return (employeeUserDetails && employeeUserDetails.length>0) ? 1 : 0;
        }); 

        let empUserUpdateJson = {
            User_Name: userDetails['User_Name'],
            Firebase_Uid: userDetails['Firebase_Uid'],
            Last_LoggedIn: userDetails['Current_Date_Time']
        };
        //If the employee user details already exist in the emp user table
        if(isEmployeeUserDetailsExist > 0){
            //Update the employee user details
            return(
            organizationDbConnection(ehrTables.empUser)
            .update(empUserUpdateJson)
            .where('Employee_Id',employeeId)
            .then(() =>{
                return true;
            })
            .catch(function (updateCatchError) {
                console.log('Error in the updateUserLoginDetails function update-.catch block.',updateCatchError);
                throw 'SIB0129';
            })
            );
        }else{
            empUserUpdateJson.Employee_Id = employeeId;
            empUserUpdateJson.Created_Date = userDetails['Current_Date_Time'];
            
            //Insert the employee user details
            return(
                organizationDbConnection(ehrTables.empUser)
                .insert(empUserUpdateJson)
                .then(() =>{
                    return(
                        organizationDbConnection(ehrTables.empJob)
                        .update('Invitation_Status', 'Signed Up')
                        .where('Employee_Id', employeeId)
                        .then(()=>{
                            return true;
                        })
                    )
                })
                .catch(function (insertCatchError) {
                    console.log('Error in the updateUserLoginDetails function insert-.catch block.',insertCatchError);
                    throw 'SIB0130';
                })
            );
        }
    }catch(error){
        console.log('Error in the updateUserLoginDetails function main catch block.',error);
        throw error;
    }
}

module.exports={
    checkAllowUserSignin,
    getSignInEmployeeDetails,
    updateUserLoginDetails
};