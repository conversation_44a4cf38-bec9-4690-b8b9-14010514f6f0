const AWS = require('aws-sdk');

const s3 = new AWS.S3({
  region: process.env.region
});
async function uploadFileToS3(fileBuffer, fileName, orgCode, contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
  try {
    
    const s3Key = generateS3FilePath(orgCode, fileName);
    
    const uploadParams = {
      Bucket: process.env.documentsBucket,
      Key: s3Key,
      Body: fileBuffer,
      ContentType: contentType,
      ServerSideEncryption: 'AES256',
      Metadata: {
        'uploaded-by': 'salary-import-system',
        'upload-timestamp': new Date().toISOString(),
        'organization-code': orgCode
      }
    };

    console.log(`Uploading file to S3: ${s3Key}`);
    
    const result = await s3.upload(uploadParams).promise();
    
    console.log(`File uploaded successfully to S3: ${result.Location}`);
    
    return {
      success: true,
      s3Url: result.Location,
      s3Key: s3Key,
      bucket: process.env.documentsBucket,
      fileName: fileName
    };

  } catch (error) {
    console.error('Error uploading file to S3:', error);
    throw error;
  }
}

async function uploadSalaryImportFiles(originalFile, errorFile, orgCode) {
  try {
    console.log(`=== UPLOADING SALARY IMPORT FILES TO S3 ===`);
    console.log(`Original file: ${originalFile?.fileName}`);
    console.log(`Error file: ${errorFile?.fileName || 'N/A'}`);

    const uploadPromises = [
      uploadFileToS3(originalFile.buffer, originalFile.fileName, orgCode)
    ];

    if (errorFile) {
      uploadPromises.push(
        uploadFileToS3(errorFile.buffer, errorFile.fileName, orgCode)
      );
    }

    const uploads = await Promise.all(uploadPromises);

    const originalUpload = uploads[0];
    const errorUpload = errorFile ? uploads[1] : null;
    const result = {
      success: true,
      originalFile: {
        fileName: originalFile.fileName,
        s3Url: originalUpload.s3Url,
        s3Key: originalUpload.s3Key
      },
      errorFile: errorUpload
        ? {
            fileName: errorFile.fileName,
            s3Url: errorUpload.s3Url,
            s3Key: errorUpload.s3Key
          }
        : null,
      bucket: process.env.documentsBucket,
      folderPath: `${process.env.domainName}/${orgCode}/salary-import/`
    };

    return result;

  } catch (error) {
    console.error('Error uploading salary import files to S3:', error);
    throw error;
  }
}

/**
 * Generate S3 file path for salary import
 * @param {string} orgCode - Organization code
 * @param {string} fileName - File name
 * @returns {string} - Complete S3 path
 */
function generateS3FilePath(orgCode, fileName) {
  return `${process.env.domainName}/${orgCode}/salary-import/${fileName}`;
}

/**
 * Check if S3 bucket and credentials are configured
 * @returns {boolean} - True if S3 is properly configured
 */
function isS3Configured() {
  return !!(process.env.documentsBucket && process.env.domainName && process.env.region);
}

module.exports = {
  uploadFileToS3,
  uploadSalaryImportFiles,
  generateS3FilePath,
  isS3Configured
};
