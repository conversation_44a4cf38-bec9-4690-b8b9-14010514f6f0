
// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../../common/tablealias');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { calculateSalary } = require('../../roresolvers/salary/calculateSalary');
const { validateCommonRuleInput, validateCandidateStatus } = require('../../common/commonfunctions');

const addUpdateCandidateSalaryDetails = async (parent, args, context, info) => {
  console.log('Inside addUpdateCandidateSalaryDetails function');

  let organizationDbConnection;
  let validationError = {};

  try {
    const loginEmployeeId = context.logInEmpId;
    const orgCode = context.orgCode;
    const userIp = context.userIp;
    const partnerId = context.partnerid;
    const currentTimestamp = moment.utc().format('YYYY-MM-DD HH:mm:ss');
    // Get database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Skip access check for external endpoint
    if (process.env.endPoint !== 'external') {
      const formIdToCheck = args.formId;
      const checkRights = await commonLib.func.checkEmployeeAccessRights(
        organizationDbConnection,
        loginEmployeeId,
        null,
        '',
        'UI',
        false,
        formIdToCheck
      );

      if (args.isEditMode) {
        if (Object.keys(checkRights).length === 0 || checkRights.Role_Update !== 1) {
          throw '_DB0102';
        }
      } else {
        if (Object.keys(checkRights).length === 0 || checkRights.Role_Add !== 1) {
          throw '_DB0101';
        }
      }
    }

    // Determine if this is edit mode (check if candidate already has salary record)
    const existingRecord = await organizationDbConnection(ehrTables.candidateSalaryDetails)
      .select('Candidate_Id')
      .where('Candidate_Id', args.candidateId)
      .first();

    const isEditMode = args.isEditMode && existingRecord;
    // Get provident fund configuration (similar to employee version)
    const providentFundConfigData = await organizationDbConnection(ehrTables.orgDetails)
      .select('Provident_Fund_Configuration')
      .first();
    const providentFundConfigurationValue = providentFundConfigData?.Provident_Fund_Configuration;
 
    if(args.isEditMode){
      await validateCandidateStatus(organizationDbConnection, args.candidateId, 'update', partnerId);
    }
    // Process the salary details
    const result = await organizationDbConnection.transaction(async (trx) => {
      let insertId;

      if (isEditMode) {
        // Update existing candidate salary record
        await updateCandidateSalaryDetails(
          organizationDbConnection,
          args,
          loginEmployeeId,
          currentTimestamp,
          orgCode,
          userIp,
          trx
        );
        insertId = args.candidateId;
      } else {
        // Insert new candidate salary record
        insertId = await insertCandidateSalaryDetails(
          organizationDbConnection,
          args,
          loginEmployeeId,
          currentTimestamp,
          orgCode,
          userIp,
          trx,
          partnerId
        );
      }

      // Add validation for allowances and retirals (similar to employee version)
      if (args.allowance && args.allowance.length > 0) {
        for (let item of args.allowance) {
          if (item.allowanceType && item.allowanceType.toLowerCase() === 'percentage' && (!item.allowanceWages || item.allowanceWages.trim() === '')) {
            throw 'IVE0615'; // Allowance wages required for percentage type
          }
        }
      }

      if (args.retirals && args.retirals.length > 0) {
        for (let item of args.retirals) {
          if (item.retiralsType && item.retiralsType.toLowerCase() === 'percentage' &&
              (!item.employeeRetiralWages || item.employeeRetiralWages.trim() === '') &&
              (!item.employerRetiralWages || item.employerRetiralWages.trim() === '')) {
            throw 'IVE0616';
          }
        }
      }

      // Calculate candidate salary after insert/update (similar to employee version)
      try {
        console.log('Calculating candidate salary for validation...');

        // Prepare salary details for calculation (matching employee version structure)
        const salaryDetails = {
          Candidate_Id: args.candidateId,
          Template_Id: args.templateId,
          Annual_Ctc: parseFloat(args.annualCTC),
          Effective_From: args.effectiveFrom,
          Annual_Gross_Salary: args.annualGrossSalary ? parseFloat(args.annualGrossSalary) : null,
          Monthly_Gross_Salary: args.monthlyGrossSalary ? parseFloat(args.monthlyGrossSalary) : null,
          Salary_Effective_Month: args.salaryEffectiveMonth || null,
          ESI_Contribution_End_Date: null,
          Status: 'Active'
        };

        // Format allowance details exactly as expected by calculateSalary
        const allowanceDetails = args.allowance ? args.allowance.map(item => ({
          Allowance_Type_Id: item.allowanceTypeId,
          Allowance_Type: item.allowanceType,
          Allowance_Wages: item.allowanceWages ? parseFloat(item.allowanceWages) : null,
          Percentage: item.percentage ? parseFloat(item.percentage) : null,
          Amount: item.amount ? parseFloat(item.amount) : null
        })) : [];

        // Format retiral details exactly as expected by calculateSalary (matching employee version)
        const retiralDetails = args.retirals ? args.retirals.map(item => ({
          Form_Id: parseInt(item.formId),
          Retirals_Id: parseInt(item.retiralsId), // Fixed: use retiralsId not retiralsTypeId
          Retirals_Type: item.retiralsType,
          Employee_Retiral_Wages: item.employeeRetiralWages ? parseFloat(item.employeeRetiralWages) : null,
          Employer_Retiral_Wages: item.employerRetiralWages ? parseFloat(item.employerRetiralWages) : null,
          Employee_Share_Percentage: item.employeeSharePercentage ? parseFloat(item.employeeSharePercentage) : null,
          Employer_Share_Percentage: item.employerSharePercentage ? parseFloat(item.employerSharePercentage) : null,
          Employee_Share_Amount: item.employeeShareAmount ? parseFloat(item.employeeShareAmount) : null,
          Employer_Share_Amount: item.employerShareAmount ? parseFloat(item.employerShareAmount) : null,
          PF_Employee_Contribution: item.pfEmployeeContribution || null,
          PF_Employer_Contribution: item.pfEmployerContribution || null,
          Employee_Statutory_Limit: item.employeeStatutoryLimit ? parseFloat(item.employeeStatutoryLimit) : null,
          Employer_Statutory_Limit: item.employerStatutoryLimit ? parseFloat(item.employerStatutoryLimit) : null,
          Eligible_For_EPS: item.eligibleForEPS || 0,
          Contribute_EPF_Actual_PF_Wage: item.contributeEpfActualPfWage || 0, // Fixed: use number not string
          Admin_Charge: item.adminCharge ? parseFloat(item.adminCharge) : null,
          EDLI_Charge: item.edliCharge ? parseFloat(item.edliCharge) : null
        })) : [];

        const contextForCalculation = {
          orgCode: orgCode,
          orgdb: organizationDbConnection
        };
        const calculationResult = await calculateSalary(
          null, // parent
          {
            employeeId: 0,
            candidateId: args.candidateId,
            retiralDetails: JSON.stringify(retiralDetails),
            allowanceDetails: JSON.stringify(allowanceDetails),
            salaryDetails: JSON.stringify(salaryDetails),
            providentFundConfigurationValue: providentFundConfigurationValue
          },
          contextForCalculation,
          null // info
        );

        if (calculationResult.errorCode) {
          console.warn('Salary calculation warning:', calculationResult.message);
          // Don't throw error, just log the warning
        } else {
          console.log('Candidate salary calculation completed successfully');
        }

      } catch (calculationError) {
         console.error('Error during candidate salary calculation:', calculationError);
         throw calculationError;
      }

      if (isEditMode) {
        return {
          errorCode: "",
          message: "Candidate salary details updated successfully"
        };
      } else {
        return {
          errorCode: "",
          message: "Candidate salary details added successfully"
        };
      }
    });
    
    return result;
    
  } catch (error) {
    console.error('Error in addUpdateCandidateSalaryDetails:', error);

    if (error === 'IVE0000') {
      const errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError: validationError });
    } else {
      let errorCode = error?.code === 'ER_DUP_ENTRY' ? 'PFF0024' : error || 'PFF0025';
      const errResult = commonLib.func.getError(errorCode, 'PFF0025');

      if (errorCode === 'PFF0024') {
        errResult.message = 'Candidate already has salary details. Please use edit mode to update existing salary.';
      }

      throw new ApolloError(errResult.message, errResult.code);
    }
  } finally {
    if (organizationDbConnection) {
      organizationDbConnection.destroy();
    }
  }
};

async function insertCandidateSalaryDetails(organizationDbConnection, args, loginEmployeeId, currentTimestamp, orgCode, userIp, trx, partnerId = null) {
  // Check if candidate already has salary details
  const existingSalary = await organizationDbConnection(ehrTables.candidateSalaryDetails)
    .where('Candidate_Id', args.candidateId)
    .first()
    .transacting(trx);
  
  if (existingSalary) {
    throw 'PFF0024'; // Candidate already has salary details
  }
  
  // Prepare main table data
  const mainTableData = {
    Candidate_Id: args.candidateId,
    Template_Id: args.templateId,
    Effective_From: args.effectiveFrom || null,
    Effective_To: null, // Always null for active salary
    Annual_Ctc: args.annualCTC,
    Annual_Gross_Salary: args.annualGrossSalary,
    Monthly_Gross_Salary: args.monthlyGrossSalary,
    Salary_Effective_Month: args.salaryEffectiveMonth || null,
    Added_On: currentTimestamp,
    Added_By: loginEmployeeId
  };
  
  // Insert main record
  await organizationDbConnection(ehrTables.candidateSalaryDetails)
    .insert(mainTableData)
    .transacting(trx);

  // Insert allowance components
  if (args.allowance && args.allowance.length > 0) {
    await insertCandidateAllowanceComponents(organizationDbConnection, args.allowance, args.candidateId, loginEmployeeId, currentTimestamp, trx);
  }

  // Insert retiral components
  if (args.retirals && args.retirals.length > 0) {
    await insertCandidateRetiralComponents(organizationDbConnection, args.retirals, args.candidateId, loginEmployeeId, currentTimestamp, trx);
  }

  // Insert gross components
  if (args.gross && args.gross.length > 0) {
    await insertCandidateGrossComponents(organizationDbConnection, args.gross, args.candidateId, trx);
  }

  const canvasIntegrationEnabled = await commonLib.func.integrationAPIIsEnabled(organizationDbConnection, {integrationType: 'Canvas', direction: 'Pull', action: 'Retrieve', entityType: 'Candidate-Salary-Not-Status-Update'});
  if(!canvasIntegrationEnabled){
    // BUSINESS RULE 1: Update candidate status to "Hired with compensation" ONLY during ADD operation
    // This status change enables the candidate to appear in DocuSign listing for offer letter generation
    const statusRecord = await organizationDbConnection('ats_status_table')
      .select('Id')
      .where('Status', 'Hired with compensation')
      .where('Form_Id', 16)
      .first();

    const candidateStatusId = statusRecord ? statusRecord.Id : null;

    if (candidateStatusId) {
      await organizationDbConnection(ehrTables.candidateRecruitmentInfo)
        .transacting(trx)
        .update({
          Candidate_Status: candidateStatusId
        })
        .where('Candidate_Id', args.candidateId);
    }
  }

  return args.candidateId;
}

/**
 * Insert candidate allowance components
 */
async function insertCandidateAllowanceComponents(organizationDbConnection, allowances, candidateId, loginEmployeeId, currentTimestamp, trx) {
  const allowanceData = allowances.map(allowance => ({
    Candidate_Id: candidateId,
    Allowance_Id: 0, // Added to match synchronized schema
    Allowance_Type_Id: allowance.allowanceTypeId,
    Allowance_Type: allowance.allowanceType || null, // Now varchar(10) to match employee table
    Percentage: allowance.percentage || null, // Now float to match employee table
    Amount: allowance.amount || 0,
    Allowance_Wages: parseFloat(allowance.allowanceWages) || null, // Now decimal(15,2) to match employee table
    FBP_Max_Declaration: allowance.fbpMaxDeclaration || null
  }));

  await organizationDbConnection(ehrTables.candidateSalaryAllowance)
    .insert(allowanceData)
    .transacting(trx);
}

/**
 * Insert candidate retiral components
 */
async function insertCandidateRetiralComponents(organizationDbConnection, retirals, candidateId, loginEmployeeId, currentTimestamp, trx) {
  const retiralData = retirals.map(retiral => ({
    Candidate_Id: candidateId,
    Form_Id: retiral.formId || null,
    Retirals_Id: retiral.retiralsId || null, // Fixed: use Retirals_Id to match synchronized schema
    Retirals_Type: retiral.retiralsType || null,
    Employee_Retiral_Wages: retiral.employeeRetiralWages || null, // Added to match employee table
    Employer_Retiral_Wages: retiral.employerRetiralWages || null, // Added to match employee table
    Employee_Share_Percentage: retiral.employeeSharePercentage || null,
    Employer_Share_Percentage: retiral.employerSharePercentage || null,
    Employee_Share_Amount: retiral.employeeShareAmount || null,
    Employer_Share_Amount: retiral.employerShareAmount || null,
    PF_Employee_Contribution: retiral.pfEmployeeContribution || null, // Now varchar(20) to match employee table
    PF_Employer_Contribution: retiral.pfEmployerContribution || null, // Now varchar(20) to match employee table
    Employee_Statutory_Limit: retiral.employeeStatutoryLimit || null, // Now decimal(7,2) to match employee table
    Employer_Statutory_Limit: retiral.employerStatutoryLimit || null, // Now decimal(7,2) to match employee table
    Eligible_For_EPS: retiral.eligibleForEPS || 0, // Now tinyint(1) to match employee table
    Contribute_EPF_Actual_PF_Wage: retiral.contributeEpfActualPfWage || 0, // New field - tinyint(1)
    Admin_Charge: retiral.adminCharge || null, // Now decimal(5,2) to match employee table
    EDLI_Charge: retiral.edliCharge || null // Now decimal(5,2) to match employee table
  }));

  await organizationDbConnection(ehrTables.candidateSalaryRetirals)
    .insert(retiralData)
    .transacting(trx);
}

/**
 * Insert candidate gross components
 */
async function insertCandidateGrossComponents(organizationDbConnection, gross, candidateId, trx) {
  const grossData = gross.map(item => ({
    Candidate_Id: candidateId,
    Gross_Id: item.grossId,
    Amount: item.amount
  }));

  await organizationDbConnection(ehrTables.candidateGrossComponents)
    .insert(grossData)
    .transacting(trx);
}

/**
 * Update existing candidate salary details
 */
async function updateCandidateSalaryDetails(organizationDbConnection, args, loginEmployeeId, currentTimestamp, orgCode, userIp, trx) {
  // Update main table
  const mainTableData = {
    Template_Id: args.templateId,
    Effective_From: args.effectiveFrom || null,
    Annual_Ctc: args.annualCTC,
    Annual_Gross_Salary: args.annualGrossSalary,
    Monthly_Gross_Salary: args.monthlyGrossSalary,
    Salary_Effective_Month: args.salaryEffectiveMonth || null,
    Updated_On: currentTimestamp,
    Updated_By: loginEmployeeId
  };

  await organizationDbConnection(ehrTables.candidateSalaryDetails)
    .where('Candidate_Id', args.candidateId)
    .update(mainTableData)
    .transacting(trx);

  // Handle allowances - delete and reinsert
  if (args.allowance !== undefined) {  // If allowance parameter is provided (even if empty array)
    // Always delete existing allowances first to ensure clean state
    await organizationDbConnection(ehrTables.candidateSalaryAllowance)
      .where('Candidate_Id', args.candidateId)
      .delete()
      .transacting(trx);

    // Insert new allowances only if array has items
    if (args.allowance.length > 0) {
      await insertCandidateAllowanceComponents(organizationDbConnection, args.allowance, args.candidateId, loginEmployeeId, currentTimestamp, trx);
    }
  }

  // Handle retirals - delete and reinsert
  if (args.retirals !== undefined) {  // If retirals parameter is provided (even if empty array)
    // Always delete existing retirals first to ensure clean state
    await organizationDbConnection(ehrTables.candidateSalaryRetirals)
      .where('Candidate_Id', args.candidateId)
      .delete()
      .transacting(trx);

    // Insert new retirals only if array has items
    if (args.retirals.length > 0) {
      await insertCandidateRetiralComponents(organizationDbConnection, args.retirals, args.candidateId, loginEmployeeId, currentTimestamp, trx);
    }
  }

  // Handle gross - delete and reinsert
  if (args.gross !== undefined) {  // If gross parameter is provided (even if empty array)
    // Always delete existing gross first to ensure clean state
    await organizationDbConnection(ehrTables.candidateGrossComponents)
      .where('Candidate_Id', args.candidateId)
      .delete()
      .transacting(trx);

    // Insert new gross only if array has items
    if (args.gross.length > 0) {
      await insertCandidateGrossComponents(organizationDbConnection, args.gross, args.candidateId, trx);
    }
  }
}

// Export resolvers
const resolvers = {
  Mutation: {
    addUpdateCandidateSalaryDetails
  }
};

module.exports = { resolvers };
