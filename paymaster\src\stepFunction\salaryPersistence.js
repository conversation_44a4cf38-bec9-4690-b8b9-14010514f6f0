const { ehrTables } = require('../common/tablealias');
const { calculateSalary } = require('../roresolvers/salary/calculateSalary');
const { addUpdateSalaryDetails } = require('../resolvers/addUpdateSalaryDetails');
const { getRoundOffValue, getRoundOffSettings, getBasicPay, getTemplateBasicPay } = require('../common/commonfunctions');
const { formId } = require('../common/appconstants');
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;

const processSalaryRevisionRecords = async (
  organizationDbConnection,
  salaryRevisionRecords,
  loginEmployeeId,
  orgCode,
  options = {}
) => {
  try {
    const successRecords = [];
    const errorRecords = [];
    const revisionWithoutArrear =
      options.revisionWithoutArrear === false ? false : true;

    let allowCurrentCtcLowerThanPrevious = await organizationDbConnection(ehrTables.payrollGeneralSettings)
      .select('Allow_Current_CTC_Lower_Than_Previous')
      .first();

    allowCurrentCtcLowerThanPrevious =
      allowCurrentCtcLowerThanPrevious?.Allow_Current_CTC_Lower_Than_Previous?.toLowerCase() === 'no'
        ? false
        : true;

    // Loop through each record
    for (let i = 0; i < salaryRevisionRecords.length; i++) {
      const record = salaryRevisionRecords[i];

      try {
        const result = await processSingleRecord(
          organizationDbConnection,
          record,
          loginEmployeeId,
          orgCode,
          allowCurrentCtcLowerThanPrevious,
          { revisionWithoutArrear }
        );
        if (result.success) {
          successRecords.push({
            ...record,
            revisionId: result.revisionId,
            calculatedCtc: result.calculatedCtc
          });
        } else {
          errorRecords.push({
            ...record,
            errorCode: result.errorCode,
            errorMessage: result.message
          });
        }
      } catch (error) {
        console.error(`Error processing record ${i + 1}:`, error);
        errorRecords.push({
          ...record,
          errorCode: 'PROCESSING_ERROR',
          errorMessage: error.message || 'Unknown processing error'
        });
      }
    }

    return {
      success: true,
      totalRecords: salaryRevisionRecords.length,
      successRecords,
      errorRecords,
      successCount: successRecords.length,
      errorCount: errorRecords.length
    };

  } catch (error) {
    console.error('Error in processSalaryRevisionRecords:', error);
    return {
      success: false,
      errorCode: 'BATCH_PROCESSING_ERROR',
      message: error.message || 'Failed to process salary revision records'
    };
  }
};

/**
 * Process single salary revision record with 4-step process
 */
const processSingleRecord = async (
  organizationDbConnection,
  record,
  loginEmployeeId,
  orgCode,
  allowCurrentCtcLowerThanPrevious = true,
  options = {}
) => {
  const {
    Employee_Id,
    Employee_Name,
    Salary_Template,
    Previous_Annual_Ctc,
    Revise_By,
    Amount_Or_Percentage
  } = record;
  const revisionWithoutArrear =
    options.revisionWithoutArrear === false ? false : true;

  // STEP 1: Data formation for calculateSalary
  const roundOffSettings = await getRoundOffSettings(organizationDbConnection);
  let newAnnualCtc;

  const previousCtc = Number(Previous_Annual_Ctc);
  const delta = Number(Amount_Or_Percentage);
  if (!Number.isFinite(previousCtc) || !Number.isFinite(delta)) {
    return {
      success: false,
      errorCode: 'IVE0002', // align with existing validation codes
      message: 'Invalid numeric values for Previous_Annual_Ctc or Amount_Or_Percentage'
    };
  }

  if (Revise_By === 'Amount') {
     if (delta < -previousCtc) { // check if delta is less than -previousCtc to avoid negative CTC
       return {
         success: false,
         errorCode: 'IVE0003',
         message: 'Amount_Or_Percentage cannot reduce CTC below zero'
       };

     }

    newAnnualCtc = previousCtc + delta; // calculate new CTC based on previous CTC and delta
  } else if (Revise_By === 'Percentage') {
     if (delta < -99) {
        return {
          success: false,
          errorCode: 'IVE0004',
          message: 'Amount_Or_Percentage cannot reduce CTC below zero'
        };
    }
    newAnnualCtc = previousCtc + (previousCtc * delta / 100); // calculate new CTC based on previous CTC and delta percentage
    
  } else {
    return {
      success: false,
      errorCode: 'IVE0001',
      message: 'Invalid Revise_By value. Must be "Amount" or "Percentage"'
    };
  }
  newAnnualCtc = getRoundOffValue(formId.salary, newAnnualCtc, roundOffSettings);

  if (!allowCurrentCtcLowerThanPrevious && newAnnualCtc <= previousCtc) {
    return {
      success: false,
      errorCode: 'IVE0005',
      message: 'New CTC must be greater than previous CTC'
    };
  }
  // Fetch template data
  const templateAllowances = await organizationDbConnection(ehrTables.templateAllowanceComponents)
    .where('Template_Id', Salary_Template)
    .select('*');

  const templateRetirals = await organizationDbConnection(ehrTables.templateRetiralComponents)
    .where('Template_Id', Salary_Template)
    .select('*');

  const templateGross = await organizationDbConnection(ehrTables.templateGrossComponents)
    .where('Template_Id', Salary_Template)
    .select('*');

  // Get PF configuration
  const providentFundConfigData = await organizationDbConnection(ehrTables.orgDetails)
    .select('Provident_Fund_Configuration')
    .first();

  const providentFundConfigurationValue = providentFundConfigData?.Provident_Fund_Configuration;

  // Get effective dates
  const employeeSalaryType = await commonLib.payroll.getEmployeeSalaryType(organizationDbConnection, Employee_Id);
  const maxPayslipMonth = await commonLib.func.maxPayslipMonth(organizationDbConnection, Employee_Id, employeeSalaryType);

  let effectiveFrom, payoutMonth;
  if (maxPayslipMonth) {
    const [year, month] = maxPayslipMonth.split('-');
    const nextMonth = parseInt(month) + 1;
    const nextYear = nextMonth > 12 ? parseInt(year) + 1 : parseInt(year);
    const finalMonth = nextMonth > 12 ? 1 : nextMonth;

    effectiveFrom = `${nextYear}-${String(finalMonth).padStart(2, '0')}-01`;
    payoutMonth = `${finalMonth},${nextYear}`;
  } else {
    const now = new Date();
    const nextMonth = now.getMonth() + 2;
    const nextYear = nextMonth > 12 ? now.getFullYear() + 1 : now.getFullYear();
    const finalMonth = nextMonth > 12 ? 1 : nextMonth;

    effectiveFrom = `${nextYear}-${String(finalMonth).padStart(2, '0')}-01`;
    payoutMonth = `${finalMonth},${nextYear}`;
  }

  // Transform template data to calculateSalary format (exact structure as per your specification)
  const allowanceDetails = templateAllowances.map(item => ({
    Allowance_Type_Id: item.Allowance_Type_Id,
    Allowance_Type: item.Allowance_Type,
    Percentage: item.Percentage,
    Amount: item.Amount
  }));

  const retiralDetails = templateRetirals.map(item => ({
    Form_Id: item.Form_Id,
    Retirals_Id: item.Retirals_Id,
    Retirals_Type: item.Retirals_Type,
    Employee_Share_Percentage: item.Employee_Share_Percentage?.toString() || "0",
    Employer_Share_Percentage: item.Employer_Share_Percentage?.toString() || "0",
    Employee_Share_Amount: item.Employee_Share_Amount || 0,
    Employer_Share_Amount: item.Employer_Share_Amount || 0,
    PF_Employee_Contribution: item.PF_Employee_Contribution || null,
    PF_Employer_Contribution: item.PF_Employer_Contribution || null,
    Employee_Statutory_Limit: item.Employee_Statutory_Limit || null,
    Employer_Statutory_Limit: item.Employer_Statutory_Limit || null,
    Admin_Charge: item.Admin_Charge || null,
    EDLI_Charge: item.EDLI_Charge || null
  }));

  const Basic_Pay= await getBasicPay(organizationDbConnection, Employee_Id);
  const salaryDetails = {
    Annual_Ctc: newAnnualCtc,
    Basic_Pay: Basic_Pay && Basic_Pay.Basic_Pay? Basic_Pay.Basic_Pay : 0  ,
    Effective_From: "",
    Effective_To: "",
    ESI_Contribution_End_Date: null,
    Status: "Active"
  };

  const grossIds = templateGross.map(item => item.Gross_Id);
  const context = {
    orgCode: orgCode,
    orgdb: organizationDbConnection
  };

  const calculateSalaryResult = await calculateSalary(
    null, // parent
    {
      employeeId: Employee_Id,
      retiralDetails: JSON.stringify(retiralDetails),
      allowanceDetails: JSON.stringify(allowanceDetails),
      salaryDetails: JSON.stringify(salaryDetails),
      providentFundConfigurationValue: providentFundConfigurationValue || "Current",
      grossIds: grossIds,
      revisionWithoutArrear: revisionWithoutArrear
    },
    context,
    null // info
  );

  if (calculateSalaryResult.errorCode) {
    console.error('calculateSalary failed:', calculateSalaryResult.message);
    return {
      success: false,
      errorCode: calculateSalaryResult.errorCode,
      message: calculateSalaryResult.message || 'Failed to calculate salary structure'
    };
  }


const salaryStructure = JSON.parse(calculateSalaryResult.salaryStructure);
const employeeRetiralDetails = JSON.parse(
  calculateSalaryResult.employeeRetiralDetails
);

const grossAmountArray = (salaryStructure?.grossAmount || []).map(item => ({
  grossId: item.Gross_Id,
  amount: item.Amount
}));

const allowanceArray = employeeRetiralDetails.employeeSalaryAllowance || [];
const bonusArray = employeeRetiralDetails.employeeSalaryBonus || [];

const finalAllowanceArray = [...allowanceArray, ...bonusArray].map(item => ({
  allowanceTypeId: item.Allowance_Type_Id,
  allowanceType: item.Allowance_Type,
  percentage: item.Percentage ?? null,
  amount: item.Amount?.toString(),
  allowanceWages: item.Allowance_Wage?.toString() ?? '0'
}));

const finalRetiralsArray = (employeeRetiralDetails.employeeSalaryRetirals || []).map(item => ({
  formId: item.Form_Id,
  retiralsId: item.Retirals_Id,
  retiralsType: item.Retiral_Type,
  employeeSharePercentage: item.Employee_Share_Percentage,
  employerSharePercentage: item.Employer_Share_Percentage,
  employeeShareAmount: item.Employee_Share_Amount?.toString(),
  employerShareAmount: item.Employer_Share_Amount?.toString(),
  employeeRetiralWages: item.Employee_Retiral_Wages?.toString(),
  employerRetiralWages: item.Employer_Retiral_Wages?.toString(),
  pfEmployeeContribution: item.PF_Employee_Contribution ?? null,
  pfEmployerContribution: item.PF_Employer_Contribution ?? null,
  employeeStatutoryLimit: item.Employee_Statutory_Limit?.toString() ?? null,
  employerStatutoryLimit: item.Employer_Statutory_Limit?.toString() ?? null,
  eligibleForEPS: item.Eligible_For_EPS ?? 0,
  contributeEpfActualPfWage: item.Contribute_EPF_Actual_PF_Wage ?? 0,
  adminCharge: item.Admin_Charge?.toString() ?? '',
  edliCharge: item.EDLI_Charge?.toString() ?? '',
  contributionEndMonth: item.Contribution_End_Month || null,
  contributionPeriodCompleted: item.Contribution_Period_Completed || null
}));

const addUpdateSalaryArgs = {
  formId: 360,
  accessFormId: 360,
  isEditMode: false,
  employeeId: Employee_Id,
  templateId: Salary_Template,
  annualCTC: newAnnualCtc.toString(),
  annualGrossSalary: ((salaryStructure?.grossSalary ?? 0) * 12).toString(),
  monthlyGrossSalary: salaryStructure?.grossSalary != null ? salaryStructure.grossSalary.toString() : '',
  effectiveFrom,
  effectiveTo: '',
  salaryEffectiveMonth: payoutMonth,
  salaryEffectiveTo: '',
  payoutMonth,
  revisionType: Revise_By === 'Percentage' ? 'Percentage' : 'Amount',
  revisionStatus: 'Applied',
  previousCtc: Previous_Annual_Ctc.toString(),
  reviseCtcByPercentage: Amount_Or_Percentage.toString(),
  revisionWithoutArrear: revisionWithoutArrear,
  allowance: finalAllowanceArray,
  retirals: finalRetiralsArray,
  gross: grossAmountArray,
  isImport: true
};

  const mockContext = {
    orgdb: organizationDbConnection,
    logInEmpId: loginEmployeeId,
    orgCode: orgCode
  };

  const addUpdateResult = await addUpdateSalaryDetails(
    null, // parent
    addUpdateSalaryArgs,
    mockContext
  );

  // Check if result has error (import mode returns error object instead of throwing)
  if (addUpdateResult && addUpdateResult.errorCode) {
    console.error('addUpdateSalaryDetails failed:', addUpdateResult.errorMessage);
    return {
      success: false,
      errorCode: addUpdateResult.errorCode,
      message: addUpdateResult.errorMessage || 'Failed to persist salary revision'
    };
  }

  // Return success result
  return {
    success: true,
    revisionId: addUpdateResult?.revisionId || null,
    calculatedCtc: newAnnualCtc,
    templateId: Salary_Template,
    message: 'Salary revision processed successfully'
  };
};

const processSalaryAddRecords = async (organizationDbConnection, salaryAddRecords, loginEmployeeId, orgCode) => {
  try {
    const successRecords = [];
    const errorRecords = [];

    for (let i = 0; i < salaryAddRecords.length; i++) {
      const record = salaryAddRecords[i];

      try {
        const result = await processSingleSalaryAddRecord(
          organizationDbConnection,
          record,
          loginEmployeeId,
          orgCode
        );

        if (result.success) {
          successRecords.push({
            ...record,
            salaryId: result.salaryId,
            calculatedCtc: result.calculatedCtc
          });
        } else {
          errorRecords.push({
            ...record,
            errorCode: result.errorCode,
            errorMessage: result.message
          });
        }
      } catch (error) {
        console.error(`Error processing add record ${i + 1}:`, error);
        errorRecords.push({
          ...record,
          errorCode: 'PROCESSING_ERROR',
          errorMessage: error.message || 'Unknown processing error'
        });
      }
    }

    return {
      success: true,
      totalRecords: salaryAddRecords.length,
      successRecords,
      errorRecords,
      successCount: successRecords.length,
      errorCount: errorRecords.length
    };
  } catch (error) {
    console.error('Error in processSalaryAddRecords:', error);
    return {
      success: false,
      errorCode: 'BATCH_PROCESSING_ERROR',
      message: error.message || 'Failed to process salary add records'
    };
  }
};

const processSingleSalaryAddRecord = async (organizationDbConnection, record, loginEmployeeId, orgCode) => {
  const {
    Employee_Id,
    Employee_Name,
    Salary_Template,
    Annual_Ctc
  } = record;

  const templateBasicPay = await getTemplateBasicPay(organizationDbConnection, Salary_Template);

  const annualCtcValue = Number(Annual_Ctc);
  if (!Number.isFinite(annualCtcValue) || annualCtcValue <= 0) {
    return {
      success: false,
      errorCode: 'IVE0002',
      message: 'Invalid numeric value for Annual_Ctc'
    };
  }
  const roundOffSettings = await getRoundOffSettings(organizationDbConnection);
  const roundedAnnualCtc = getRoundOffValue(formId.salary, annualCtcValue, roundOffSettings);

  // Fetch template data
  const templateAllowances = await organizationDbConnection(ehrTables.templateAllowanceComponents)
    .where('Template_Id', Salary_Template)
    .select('*');

  const templateRetirals = await organizationDbConnection(ehrTables.templateRetiralComponents)
    .where('Template_Id', Salary_Template)
    .select('*');

  const templateGross = await organizationDbConnection(ehrTables.templateGrossComponents)
    .where('Template_Id', Salary_Template)
    .select('*');

  // Get PF configuration
  const providentFundConfigData = await organizationDbConnection(ehrTables.orgDetails)
    .select('Provident_Fund_Configuration')
    .first();

  const providentFundConfigurationValue = providentFundConfigData?.Provident_Fund_Configuration;

  // Get employee Date of Join for effectiveFrom
  const employeeJobDetails = await organizationDbConnection(ehrTables.empJob)
    .select('Date_Of_Join')
    .where('Employee_Id', Employee_Id)
    .first();

  const effectiveFrom = employeeJobDetails?.Date_Of_Join || null;
  if (!effectiveFrom) {
    return {
      success: false,
      errorCode: 'IVE0617',
      message: 'Date of Join not found for employee'
    };
  }

  // Transform template data to calculateSalary format
  const allowanceDetails = templateAllowances.map(item => ({
    Employee_Salary_Id:Employee_Id,
    Allowance_Type_Id: item.Allowance_Type_Id,
    Allowance_Type: item.Allowance_Type,
    Percentage: item.Percentage,
    Amount: item.Amount
  }));

  const retiralDetails = templateRetirals.map(item => ({
    Employee_Salary_Id:Employee_Id,
    Form_Id: item.Form_Id,
    Retirals_Id: item.Retirals_Id,
    Retirals_Type: item.Retirals_Type,
    Employee_Share_Percentage: item.Employee_Share_Percentage?.toString() || "0",
    Employer_Share_Percentage: item.Employer_Share_Percentage?.toString() || "0",
    Employee_Share_Amount: item.Employee_Share_Amount || 0,
    Employer_Share_Amount: item.Employer_Share_Amount || 0,
    PF_Employee_Contribution: item.PF_Employee_Contribution || null,
    PF_Employer_Contribution: item.PF_Employer_Contribution || null,
    Employee_Statutory_Limit: item.Employee_Statutory_Limit || null,
    Employer_Statutory_Limit: item.Employer_Statutory_Limit || null,
    Admin_Charge: item.Admin_Charge || null,
    EDLI_Charge: item.EDLI_Charge || null
  }));

  const salaryDetails = {
    Employee_Id: Employee_Id,
    Annual_Ctc: roundedAnnualCtc,
    Basic_Pay: templateBasicPay?.Basic_Pay || 0,
    Effective_From: effectiveFrom,
    Effective_To: "",
    ESI_Contribution_End_Date: null,
    Status: "Active"
  };

  const grossIds = templateGross.map(item => item.Gross_Id);
  const context = {
    orgCode: orgCode,
    orgdb: organizationDbConnection
  };

  const calculateSalaryResult = await calculateSalary(
    null,
    {
      employeeId: Employee_Id,
      retiralDetails: JSON.stringify(retiralDetails),
      allowanceDetails: JSON.stringify(allowanceDetails),
      salaryDetails: JSON.stringify(salaryDetails),
      providentFundConfigurationValue: providentFundConfigurationValue || "Current",
      grossIds: grossIds,
      revisionWithoutArrear: false
    },
    context,
    null
  );

  if (calculateSalaryResult.errorCode) {
    console.error('calculateSalary failed:', calculateSalaryResult.message);
    return {
      success: false,
      errorCode: calculateSalaryResult.errorCode,
      message: calculateSalaryResult.message || 'Failed to calculate salary structure'
    };
  }

  const salaryStructure = JSON.parse(calculateSalaryResult.salaryStructure);
  const employeeRetiralDetails = JSON.parse(
    calculateSalaryResult.employeeRetiralDetails
  );

  const grossAmountArray = (salaryStructure?.grossAmount || []).map(item => ({
    grossId: item.Gross_Id,
    amount: item.Amount
  }));

  const allowanceArray = employeeRetiralDetails.employeeSalaryAllowance || [];
  const bonusArray = employeeRetiralDetails.employeeSalaryBonus || [];

  const finalAllowanceArray = [...allowanceArray, ...bonusArray].map(item => ({
    allowanceTypeId: item.Allowance_Type_Id,
    allowanceType: item.Allowance_Type,
    percentage: item.Percentage ?? null,
    amount: item.Amount?.toString(),
    allowanceWages: item.Allowance_Wage?.toString() ?? '0'
  }));

const finalRetiralsArray = (employeeRetiralDetails.employeeSalaryRetirals || []).map(item => ({
  formId: item.Form_Id,
  retiralsId: item.Retirals_Id,
  retiralsType: item.Retiral_Type,
  employeeSharePercentage: item.Employee_Share_Percentage,
  employerSharePercentage: item.Employer_Share_Percentage,
  employeeShareAmount: item.Employee_Share_Amount?.toString(),
  employerShareAmount: item.Employer_Share_Amount?.toString(),
  employeeRetiralWages: item.Employee_Retiral_Wages?.toString(),
  employerRetiralWages: item.Employer_Retiral_Wages?.toString(),
  pfEmployeeContribution: item.PF_Employee_Contribution ?? null,
  pfEmployerContribution: item.PF_Employer_Contribution ?? null,
  employeeStatutoryLimit: item.Employee_Statutory_Limit?.toString() ?? null,
  employerStatutoryLimit: item.Employer_Statutory_Limit?.toString() ?? null,
  eligibleForEPS: item.Eligible_For_EPS ?? 0,
  contributeEpfActualPfWage: item.Contribute_EPF_Actual_PF_Wage ?? 0,
  adminCharge: item.Admin_Charge?.toString() ?? '',
  edliCharge: item.EDLI_Charge?.toString() ?? '',
  contributionEndMonth: item.Contribution_End_Month || null,
  contributionPeriodCompleted: item.Contribution_Period_Completed || null
}));

  const addUpdateSalaryArgs = {
    formId: 207,
    accessFormId: 207,
    isEditMode: false,
    employeeId: Employee_Id,
    templateId: Salary_Template,
    annualCTC: roundedAnnualCtc.toString(),
    annualGrossSalary: ((salaryStructure?.grossSalary ?? 0) * 12).toString(),
    monthlyGrossSalary: salaryStructure?.grossSalary != null ? salaryStructure.grossSalary.toString() : '',
    effectiveFrom: effectiveFrom,
    effectiveTo: '',
    allowance: finalAllowanceArray,
    retirals: finalRetiralsArray,
    gross: grossAmountArray,
    isImport: true
  };

  const mockContext = {
    orgdb: organizationDbConnection,
    logInEmpId: loginEmployeeId,
    orgCode: orgCode
  };

  const addUpdateResult = await addUpdateSalaryDetails(
    null,
    addUpdateSalaryArgs,
    mockContext
  );

  if (addUpdateResult && addUpdateResult.errorCode) {
    console.error('addUpdateSalaryDetails failed:', addUpdateResult.errorMessage);
    return {
      success: false,
      errorCode: addUpdateResult.errorCode,
      message: addUpdateResult.errorMessage || 'Failed to persist salary details'
    };
  }

  return {
    success: true,
    salaryId: addUpdateResult?.salaryId || null,
    calculatedCtc: roundedAnnualCtc,
    templateId: Salary_Template,
    message: 'Salary add processed successfully'
  };
};

module.exports = {
  processSalaryRevisionRecords,
  processSingleRecord,
  processSalaryAddRecords,
  processSingleSalaryAddRecord
};
