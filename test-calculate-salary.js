const { calculateSalary } = require('./paymaster/src/roresolvers/salary/calculateSalary');

// Test payload that's failing (Employee ID 69)
const failingPayload = {
  employeeId: 69,
  retiralDetails: '[{"Employee_Salary_Id":69,"Form_Id":52,"Retirals_Id":0,"Retirals_Type":"Percentage","Employee_Share_Percentage":12,"Employer_Share_Percentage":12,"Employee_Share_Amount":13331,"Employer_Share_Amount":13331,"PF_Employee_Contribution":"Actual","PF_Employer_Contribution":"Actual","Employee_Statutory_Limit":null,"Employer_Statutory_Limit":null,"Admin_Charge":null,"EDLI_Charge":null}]',
  allowanceDetails: '[{"Employee_Salary_Id":69,"Allowance_Type_Id":9,"Allowance_Type":"Custom Formula","Percentage":null,"Amount":44437},{"Employee_Salary_Id":69,"Allowance_Type_Id":11,"Allowance_Type":"Fixed","Percentage":null,"Amount":96610},{"Employee_Salary_Id":69,"Allowance_Type_Id":18,"Allowance_Type":"Custom Formula","Percentage":null,"Amount":9258},{"Employee_Salary_Id":69,"Allowance_Type_Id":22,"Allowance_Type":"Custom Formula","Percentage":null,"Amount":3000},{"Employee_Salary_Id":69,"Allowance_Type_Id":23,"Allowance_Type":"Custom Formula","Percentage":null,"Amount":111091},{"Employee_Salary_Id":69,"Allowance_Type_Id":29,"Allowance_Type":"Percentage","Percentage":10,"Amount":317276}]',
  salaryDetails: '{"Employee_Id":69,"Annual_Ctc":4031930,"Basic_Pay":111091,"Effective_From":"2024-09-16","Effective_To":"","ESI_Contribution_End_Date":null,"Status":"Active"}',
  providentFundConfigurationValue: 'Current',
  grossIds: [ 1, 2 ],
  revisionWithoutArrear: false
};

// Mock context - you'll need to adjust this based on your actual database connection
const mockContext = {
  connection: {
    OrganizationDb: {
      // Add your database connection config here
      client: 'mysql2',
      connection: {
        host: 'localhost',
        user: 'your_user',
        password: 'your_password',
        database: 'your_database'
      }
    }
  }
};

async function testCalculateSalary() {
  try {
    console.log('🧪 Testing calculateSalary with failing payload...');
    const result = await calculateSalary(null, failingPayload, mockContext, null);
    console.log('✅ Success:', result);
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('❌ Code:', error.extensions?.code);
    console.error('❌ Full error:', error);
  }
}

// Run the test
testCalculateSalary();
