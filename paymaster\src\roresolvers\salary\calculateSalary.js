const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../common/tablealias');
const { formId } = require('../../common/appconstants');
const {
    getInsuranceRoundOffSettings,
    getInsuranceSpecificRoundOffValue
} = require('../../common/insuranceRoundOffFunctions');

const commonFunctions = require('../../common/commonfunctions');
const { calculateSalaryWithFormulas, calculateProvidentFundDetails ,calculateNPSDetails,getStatutorySalary} = commonFunctions;

let roundOffSettings;
let insuranceRoundOffMap;
let organizationDbConnection;
/**
 * Calculate salary for an employee
 * @param {Object} args - Arguments containing:
 *   - allowanceDetails: JSON string with format [{ Allowance_Type_Id: number, Amount: number, ... }]
 *   - retiralDetails: JSON string with retiral information
 *   - salaryDetails: JSON string with salary information
 *   - providentFundConfigurationValue: String with PF configuration
 */
module.exports.calculateSalary = async (parent, args, context, info) => {
    console.log('Inside calculateSalary function',args);
    try {
        organizationDbConnection = context.orgdb ? context.orgdb : knex(context.connection.OrganizationDb);

        let employeeRetiralDetails = {}

        let basicPay;

        //Step 1: Retrieve the input data
        const allowance = args.allowanceDetails?.length > 0 ? JSON.parse(args.allowanceDetails) : []
        const retiralDetails = args.retiralDetails?.length > 0 ? JSON.parse(args.retiralDetails) : []
        const salaryDetails = args.salaryDetails ? JSON.parse(args.salaryDetails) : {}
        const providentFundConfigurationValue = args.providentFundConfigurationValue;
        //Step 2: Retrieve the allowance details, round off settings, salary configuration and payroll general settings
        let [allowanceDetails, roundOff, salaryConfiguration, payrollGeneralSettings, insuranceRoundOff] = await Promise.all([
            getAllowanceDetails(organizationDbConnection, allowance),
            getRoundOffSettings(),
            retrieveSalaryConfiguration(organizationDbConnection, args.employeeId, args.candidateId),
            retrievePayrollGeneralSettings(organizationDbConnection),
            getInsuranceRoundOffSettings(organizationDbConnection)
        ]);
        roundOffSettings = roundOff;
        insuranceRoundOffMap = insuranceRoundOff;
        const basicPayEarning = allowanceDetails.find(item => item.Component_Code?.toLowerCase() === 'basic_salary_amount');
        //Step 3: Calculate monthly CTC (Cost to Company)
        const annualCTC = getRoundOffValue(formId.salary, salaryDetails.Annual_Ctc);
        const monthlyCTC = getRoundOffValue(formId.salary, annualCTC / 12);
        if (basicPayEarning) {
            basicPayEarning.Amount = allowance.find(item => item.Allowance_Type_Id === basicPayEarning.Allowance_Type_Id)?.Amount;
            let basicPayEarningRecord= allowance.find(item => item.Allowance_Type_Id === basicPayEarning.Allowance_Type_Id);
            if(basicPayEarningRecord && basicPayEarningRecord.Allowance_Type?.toLowerCase() === 'percentage'){
                basicPayEarning.Allowance_Wage = monthlyCTC;
            }
        }

       if (basicPayEarning) {
            // Check if basic pay uses custom formula
            if (basicPayEarning.Calculation_Type?.toLowerCase() === 'custom formula' && basicPayEarning.Custom_Formula) {
                // Basic pay uses custom formula - will be calculated in iterative loop
                // For formula-based basic pay, start with 0 and calculate in the iteration
                basicPay = 0;
            } else {
                // Use normal amount calculation
                basicPay = getRoundOffValue(formId.salary, basicPayEarning.Amount, roundOff);
                // Basic pay calculated from amount
            }
        }
        //Step 5: Calculate Allowance Amount and Total Allowance Amount
        const allowanceData = calculateAllowanceDetails(allowanceDetails, basicPay);
        let providentFundSettings = null;
        let socialSecurityScheme = null;

        const providentFundDetailsRaw = retiralDetails?.find(item => item.Form_Id === formId.pfId);

        let providentFundDetails = null;

        if (providentFundDetailsRaw) {
            const [settings, config, scheme] = await Promise.all([
                retrieveProvidentFundSettings(organizationDbConnection),
                retrieveProvidentFundConfiguration(organizationDbConnection),
                retrieveSocialSecurityScheme(organizationDbConnection)
            ]);

            providentFundSettings = settings;
            socialSecurityScheme = scheme;
            providentFundDetails = { ...providentFundDetailsRaw, ...config };
        }
        const hasFormulaBasedAllowances = allowanceData.some(allowance =>
            allowance.Allowance_Type?.toLowerCase() === 'custom formula' ||
            allowance.Calculation_Type?.toLowerCase() === 'custom formula'
        ) || (basicPayEarning?.Calculation_Type?.toLowerCase() === 'custom formula' || basicPayEarning?.Allowance_Type?.toLowerCase() === 'custom formula');
        if (hasFormulaBasedAllowances) {
            const [providentFundSettings, socialSecurityScheme] = await Promise.all([
                retrieveProvidentFundSettings(organizationDbConnection),
                retrieveSocialSecurityScheme(organizationDbConnection)
            ]);
            let value = await calculateSalaryWithFormulas(args, context, {
                allowanceDetails,
                roundOffSettings: roundOff,
                salaryConfiguration,
                payrollGeneralSettings,
                insuranceRoundOffMap: insuranceRoundOff,
                providentFundSettings,
                socialSecurityScheme,
                retiralDetails,
                providentFundDetails
            });
            value.forEach(item => {
                if (item.Calculation_Type.toLowerCase() === 'custom formula') {
                    let result = allowanceDetails.find(i => i.Allowance_Type_Id === item.Allowance_Type_Id);
                    if (result) {
                        result.Allowance_Type = 'Amount';
                        result.Amount = item.Amount;
                        if (result.Component_Code?.toLowerCase() === 'basic_salary_amount') {
                            basicPay = item.Amount;
                        }
                    }
                }
            });
        }


        // Calculate total for non-formula-based allowances only (formula-based will be calculated separately)
        const totalNonFormulaAllowanceAmount = allowanceData.reduce((total, allowance) => {
            if (allowance.Component_Code?.toLowerCase() !== 'fixed_allowance_amount') {
                return total + (allowance.Amount || 0);
            }
            return total;
        }, 0);
        // Step 7: Calculate Insurance
        let insuranceType = null;
        const insuranceDetails = retiralDetails?.filter(item => item.Form_Id === formId.insurance) || [];

        if (salaryConfiguration?.Eligible_For_Insurance && insuranceDetails.length > 0) {
            const tableInsuranceDetails = await organizationDbConnection(ehrTables.insuranceConfiguration)
                .select('*')
                .whereIn('InsuranceType_Id', insuranceDetails.map(item => item.Retirals_Id));

            const userInsuranceData = commonLib.func.organizeData(insuranceDetails, 'Retirals_Id');

            insuranceType = tableInsuranceDetails.map(insuranceDetail => {
                const currentRecord = ensureArray(userInsuranceData, insuranceDetail.InsuranceType_Id)[0];
                return {
                    ...insuranceDetail,
                    Employee_Share_Percentage: currentRecord.Employee_Share_Percentage,
                    Employer_Share_Percentage: currentRecord.Employer_Share_Percentage,
                    Employee_Share_Amount: currentRecord.Employee_Share_Amount,
                    Employer_Share_Amount: currentRecord.Employer_Share_Amount,
                    Form_Id: currentRecord.Form_Id
                };
            });
        }

        // Step 8: Calculate NPS
        let npsSlabDetails = null
        const npsRetirals = retiralDetails?.length > 0 ?
            retiralDetails.find((item) => item.Form_Id === formId.npsId) : null;

        if (npsRetirals && salaryConfiguration?.Eligible_For_Nps) {
            npsSlabDetails = await organizationDbConnection(ehrTables.npsSlab)
                .select('*')
        }

        // Step 9: Calculate Gratuity
        let gratuitySettings = null
        const gratuityRetirals = retiralDetails?.length > 0 ?
            retiralDetails.find((item) => item.Form_Id === formId.gratuityId) : null;

        if (salaryConfiguration?.Eligible_For_Gratuity && gratuityRetirals) {
            gratuitySettings = await retrieveGratuitySettings(organizationDbConnection)
        }

        // Step 10: Calculate Bonus
        let bonusAllowanceDetails = allowanceDetails.filter((item) => item.Allowance_Mode?.toLowerCase() === 'bonus');

        // Step 11: Calculate Fixed Allowance (temporarily 0 to calculate PF and Insurance properly)
        let fixedAllowance = 0;
        let pfEmployerShareAmount = 0;
        let insuranceEmployerShareAmount = 0;
        let npsEmployerShareAmount = 0;
        let gratuityAmount = 0;
        let bonusAmount = 0;
        let finalAllowanceAmount = 0;
        let adminChargeAmount = 0;
        let edliChargeAmount = 0;

        // Helper: Calculates PF and returns { response, employerAmount, adminCharge, edliCharge }
        const getProvidentFund = async () => {
            if (!providentFundDetails || !salaryConfiguration?.Eligible_For_Pf) return { response: null, employerAmount: 0, adminCharge: 0, edliCharge: 0 };
            const response = await calculateProvidentFundDetails(
                providentFundDetails,
                basicPay,
                allowanceData,
                providentFundSettings,
                socialSecurityScheme,
                payrollGeneralSettings,
                providentFundConfigurationValue,
                roundOffSettings
            );
            return {
                response,
                employerAmount: response?.Employer_Share_Amount || 0,
                adminCharge: response?.Admin_Charge || 0,
                edliCharge: response?.EDLI_Charge || 0
            };
        };

        // Helper: Calculates Gratuity
        const getGratuity = async () => {
            if (!gratuityRetirals || !gratuitySettings || !salaryConfiguration?.Eligible_For_Gratuity) return { response: null, employerAmount: 0 };
            const response = await calculateGratuityAmount(
                basicPay,
                gratuityRetirals,
                allowanceData,
                gratuitySettings
            );
            return { response, employerAmount: response?.Employer_Share_Amount || 0 };
        };

        // Helper: Calculates NPS
        const getNPS = async () => {
            if (!npsSlabDetails || !salaryConfiguration?.Eligible_For_Nps) return { response: null, employerAmount: 0 };
            const response = await calculateNPSDetails(
                allowanceData,
                basicPay,
                npsRetirals,
                npsSlabDetails,
                payrollGeneralSettings,
                roundOffSettings
            );
            return { response, employerAmount: response?.Employer_Share_Amount || 0 };
        };

        // Helper: Calculates Insurance
        const getInsurance = async () => {
            if (!insuranceDetails?.length || !salaryConfiguration?.Eligible_For_Insurance) return { response: [], employerAmount: 0 };
            const response = await calculateInsuranceDetails(
                organizationDbConnection,
                insuranceType,
                allowanceData,
                basicPay,
                salaryDetails,
                args.revisionWithoutArrear || false
            );
            let period = {
                'Quarterly': 3,
                'Half Yearly': 6,
                'Monthly': 1,
                'Annually': 12
            }
            //const employerAmount = response.reduce((total, ins) => total + ins.Employer_Share_Amount, 0);
            let employerAmount = 0;
            for (item of response) {
                if(item.Payment_Frequency && item.Retiral_Type==="Fixed"){
                employerAmount += (item.Employer_Share_Amount / period[item.Payment_Frequency]);
                }
                else{
                    employerAmount += item.Employer_Share_Amount;
                }
            }
            return { response, employerAmount };
        };
        const getBonus = () => {
            if (!bonusAllowanceDetails?.length) return { response: [], amount: 0 };
            const response = calculateBonusDetails(
                bonusAllowanceDetails,
                allowanceData,
                basicPay
            );
            //      const amount = response.reduce((total, b) => total + (b.Amount || 0), 0);
            let period = {
                'Quarterly': 3,
                'HalfYearly': 6,
                'Monthly': 1,
                'Annually': 12
            }
            let monthlyBonusAmount = 0;
            for (item of response) {
                monthlyBonusAmount += item.Amount / period[item.Period];

            }
            return { response, amount: monthlyBonusAmount };
        };

        // Step 12: --- Iterative convergence loop ---
        for (let i = 0; i < 10; i++) {
            // First, update formula-based allowances with current fixed allowance estimate
            for (let allowance of allowanceData) {
                if (allowance.Component_Code?.toLowerCase() === 'fixed_allowance_amount') {
                    allowance.Amount = fixedAllowance;
                }
            }

            // Now calculate retirals with updated allowance data
            const [pf, gratuity, nps, insurance] = await Promise.all([
                getProvidentFund(),
                getGratuity(),
                getNPS(),
                getInsurance()
            ]);
            const bonus = getBonus();
            pfEmployerShareAmount = getRoundOffValue(formId.pfId, pf.employerAmount);
            adminChargeAmount = getRoundOffValue(formId.pfId, pf.adminCharge);
            edliChargeAmount = getRoundOffValue(formId.pfId, pf.edliCharge);
            gratuityAmount = getRoundOffValue(formId.gratuityId, gratuity.employerAmount ? gratuity.employerAmount/12 : 0);
            npsEmployerShareAmount = getRoundOffValue(formId.npsId, nps.employerAmount);
            insuranceEmployerShareAmount = getRoundOffValue(formId.insurance, insurance.employerAmount);
            bonusAmount = getRoundOffValue(formId.salary, bonus.amount);

            // Calculate new fixed allowance based on updated retiral amounts
            const newFixedAllowance = monthlyCTC - (
                basicPay +
                totalNonFormulaAllowanceAmount +
                pfEmployerShareAmount +
                adminChargeAmount +
                edliChargeAmount +
                insuranceEmployerShareAmount +
                npsEmployerShareAmount +
                gratuityAmount +
                bonusAmount
            );

            const newFixedAllowancePositive = Math.max(0, newFixedAllowance);

            // Check for convergence
            const difference = Math.abs(newFixedAllowancePositive - fixedAllowance);
            fixedAllowance = newFixedAllowancePositive;

            // If converged within 1 rupee, break early
            if (difference < 1 && i > 2) {
                break;
            }
        }

        // Step 13: Final round of calculations (accurate values after convergence)
        const [finalPf, finalGratuity, finalNps, finalInsurance] = await Promise.all([
            getProvidentFund(),
            getGratuity(),
            getNPS(),
            getInsurance()
        ]);
        const finalBonus = getBonus();
        pfEmployerShareAmount = getRoundOffValue(formId.pfId, finalPf.employerAmount);
        adminChargeAmount = getRoundOffValue(formId.pfId, finalPf.adminCharge);
        edliChargeAmount = getRoundOffValue(formId.pfId, finalPf.edliCharge);
        gratuityAmount = finalGratuity.employerAmount;
        npsEmployerShareAmount = getRoundOffValue(formId.npsId, finalNps.employerAmount);
        insuranceEmployerShareAmount = getRoundOffValue(formId.insurance, finalInsurance.employerAmount);
        bonusAmount = getRoundOffValue(formId.salary, finalBonus.amount);
        let monthlyGratuityAmount = getRoundOffValue(formId.gratuityId, gratuityAmount ? gratuityAmount/12 : 0);

        // Step 14: Prepare employeeRetiralDetails
        let retirals = [];
        if (finalPf.response) retirals.push(finalPf.response);
        if (finalGratuity.response) retirals.push(finalGratuity.response);
        if (finalNps.response) retirals.push(finalNps.response);
        if (finalInsurance.response.length) retirals.push(...finalInsurance.response);
        employeeRetiralDetails['employeeSalaryRetirals'] = retirals;


        if (finalBonus.response.length) employeeRetiralDetails['employeeSalaryBonus'] = finalBonus.response;
        let finalAllowances;
        if (allowanceData.length) {
            finalAllowances = allowanceData.map((item) => {
                return {
                    Allowance_Type_Id: item.Allowance_Type_Id,
                    Allowance_Type: JSON.parse(args.allowanceDetails).find(a => a.Allowance_Type_Id === item.Allowance_Type_Id)?.Allowance_Type,
                    Percentage: item.Percentage,
                    Component_Code: item.Component_Code,
                    Is_Basic_Pay: item.Component_Code?.toLowerCase() === 'basic_salary_amount' ? 'Yes' : 'No', // Keep for backward compatibility
                    Formula_Based: item.Component_Code?.toLowerCase() === 'fixed_allowance_amount' ? 'Yes' : 'No', // Keep for backward compatibility
                    Amount: getRoundOffValue(formId.salary, item.Amount),
                    Period: item.Period,
                    Allowance_Wage: item.Allowance_Wage
                }
            });
            if (basicPayEarning) {
                let basicPayAllowance = {
                    Allowance_Type_Id: basicPayEarning.Allowance_Type_Id,
                    Allowance_Type: JSON.parse(args.allowanceDetails).find(a => a.Allowance_Type_Id === basicPayEarning.Allowance_Type_Id)?.Allowance_Type,
                    Percentage: basicPayEarning.Percentage,
                    Component_Code: 'basic_salary_amount',
                    Is_Basic_Pay: 'Yes', // Keep for backward compatibility
                    Formula_Based: 'No', // Keep for backward compatibility
                    Amount: getRoundOffValue(formId.salary, basicPayEarning.Amount),
                    Period: basicPayEarning.Period,
                    Allowance_Wage: basicPayEarning.Allowance_Wage
                }
                finalAllowances.push(basicPayAllowance)
            }
            employeeRetiralDetails['employeeSalaryAllowance'] = finalAllowances
        }

        // finalAllowanceAmount = finalAllowances.reduce((sum, a) => sum + (a.Amount || 0), 0);
        
        // const calculatedTotal = finalAllowanceAmount + pfEmployerShareAmount +
        //     insuranceEmployerShareAmount + npsEmployerShareAmount + monthlyGratuityAmount + bonusAmount;

        const totalNonFormulaAllowanceAmountAndBasic = finalAllowances.reduce((total, allowance) => {
            if (allowance.Component_Code?.toLowerCase() !== 'fixed_allowance_amount') {
                return total + (allowance.Amount || 0);
            }
            return total;
        }, 0);

        // Calculate new fixed allowance based on updated retiral amounts
        fixedAllowance = monthlyCTC - (
            totalNonFormulaAllowanceAmountAndBasic +
            pfEmployerShareAmount +
            adminChargeAmount +
            edliChargeAmount +
            insuranceEmployerShareAmount +
            npsEmployerShareAmount +
            monthlyGratuityAmount +
            bonusAmount
        );

        finalAllowanceAmount = totalNonFormulaAllowanceAmountAndBasic+fixedAllowance;

        employeeRetiralDetails['employeeSalaryAllowance']=employeeRetiralDetails['employeeSalaryAllowance'].map(item => ({
            ...item,
            Amount: item.Component_Code?.toLowerCase() === 'fixed_allowance_amount' ? getRoundOffValue(formId.salary, fixedAllowance) : item.Amount
        }));

        const calculatedTotal = finalAllowanceAmount + pfEmployerShareAmount + adminChargeAmount + edliChargeAmount +
            insuranceEmployerShareAmount + npsEmployerShareAmount + monthlyGratuityAmount + bonusAmount;

        const grossSalary = finalAllowanceAmount;
        let grossAmount = [];
        if (args.grossIds?.length) {
            const grossInclusionWithRetirals = finalAllowanceAmount + pfEmployerShareAmount + adminChargeAmount + edliChargeAmount + insuranceEmployerShareAmount + npsEmployerShareAmount + monthlyGratuityAmount;
            grossAmount = await formGrossAmount(organizationDbConnection, args.grossIds, grossSalary, grossInclusionWithRetirals);
        }
        const salaryStructure = {
            basic: basicPay,
            fixedAllowance: getRoundOffValue(formId.salary, fixedAllowance),
            finalAllowanceAmount: getRoundOffValue(formId.salary, finalAllowanceAmount),
            pfEmployerShareAmount: pfEmployerShareAmount,
            adminChargeAmount: adminChargeAmount,
            edliChargeAmount: edliChargeAmount,
            insuranceEmployerShareAmount: insuranceEmployerShareAmount,
            npsEmployerShareAmount: npsEmployerShareAmount,
            gratuityAmount: gratuityAmount,
            bonusAmount: bonusAmount,
            total: calculatedTotal,
            monthlyCTC: monthlyCTC,
            grossSalary: grossSalary,
            grossAmount: grossAmount,
        };

        let errorCode = '';
        // if (calculatedTotal - monthlyCTC > 0) errorCode = 'PST0110';
        if (fixedAllowance < 0) errorCode = 'PST0110';

        return {
            errorCode,
            message: errorCode ? 'Total calculated salary exceeds the annual CTC. Please review the salary structure.' : 'Salary calculated successfully',
            employeeRetiralDetails: JSON.stringify(employeeRetiralDetails),
            salaryStructure: JSON.stringify(salaryStructure)
        };


    } catch (err) {
        console.error('Error in calculateSalary function main catch block.', err);

        // Handle validation errors with custom messages (same pattern as addUpdatePersonalInfo.js)
        if (err instanceof Error && err.message === 'IVE0000' && err.validationError) {
            console.log('Validation error in calculateSalary function', err.validationError);

            // Get the error code and custom message from validationError object
            const errorCode = Object.keys(err.validationError)[0]; // e.g., 'IVE0724'
            const customMessage = err.validationError[errorCode];

            // Throw ApolloError with custom message
            throw new ApolloError(customMessage, errorCode);
        }

        // Handle other errors normally
        const errorInput = err instanceof Error ? err.message : err;
        let errResult = commonLib.func.getError(errorInput, 'PFF0020');
        throw new ApolloError(errResult.message, errResult.code)
    }
    finally {
        if (!context.orgdb) organizationDbConnection ? organizationDbConnection.destroy() : null
            ;
    }
}

async function formGrossAmount(organizationDbConnection, grossIds, grossSalary, grossInclusionWithRetirals) {
    try {
        let grossAmount = [];
        let grossData = await organizationDbConnection(ehrTables.grossConfiguration + ' as GC')
            .select('GC.Calculation_Type', 'SC.Component_Code', 'GC.Gross_Id')
            .whereIn('GC.Gross_Id', grossIds)
            .leftJoin(ehrTables.salaryComponents + ' as SC', 'SC.Component_Id', 'GC.Salary_Component_Id')
        console.log(grossIds, grossData)
        if (grossData.length === 0) return grossAmount;

        for (let gross of grossData) {
                if (gross.Component_Code?.toLowerCase() === 'gross_salary_amount') {
                    grossAmount.push({
                        Gross_Id: gross.Gross_Id,
                        Amount: grossSalary
                    })
                } else if (gross.Component_Code?.toLowerCase() === 'gross_inclusive_of_retirals') {
                    grossAmount.push({
                        Gross_Id: gross.Gross_Id,
                        Amount: grossInclusionWithRetirals
                    })
                }
            
        }

        return grossAmount;
    }
    catch (err) {
        console.error('Error in formGrossAmount function', err);
        throw err
    }
}

/**
 * Retrieves the provident fund configuration from the database for a given organization.
 * @param {Object} organizationDbConnection - Database connection object for the organization's database.
 * @returns {Promise<Object>} - Promise resolving to the provident fund configuration.
 * @throws Will throw an error if the database query fails.
 */
async function retrieveProvidentFundConfiguration(organizationDbConnection) {
    try {
        let providentFundConfiguration = await organizationDbConnection(ehrTables.providentFund)
            .select('*')
            .first()

        return providentFundConfiguration
    } catch (err) {
        console.error('Error in retrieveProvidentFundConfiguration function', err);
        throw err
    }
}

/**
 * Processes provident fund details and calculates contributions
 * @param {Object} options - Configuration options
 * @param {Object} options.retiralDetails - Employee's retiral details
 * @param {Object} options.salaryDetails - Employee's salary details
 * @param {Array} options.allowanceDetails - Employee's allowance details
 * @param {Object} options.providentFundSettings - Provident fund settings
 * @param {Object} options.socialSecurityScheme - Social security scheme details
 * @param {Object} options.payrollGeneralSettings - General payroll settings
 * @returns {Promise<Object>} - Processed provident fund details
 */
// async function calculateProvidentFundDetails(
//     providentFundDetails,
//     basicPay,
//     allowanceDetails,
//     providentFundSettings,
//     socialSecurityScheme,
//     payrollGeneralSettings,
//     providentFundConfigurationValue
// ) {
//     try {
//         const employeePfWage = getPfSalary(
//             basicPay,
//             allowanceDetails,
//             providentFundSettings,
//             providentFundDetails,
//             basicPay,
//             0, // pfAdhocAllowanceAmount
//             providentFundConfigurationValue
//         );

//         let providentFundResponse;
//         if (payrollGeneralSettings?.Slab_Wise_PF?.toLowerCase() === 'yes') {
//             providentFundResponse = await calculateSlabWisePf(socialSecurityScheme, employeePfWage);
//         } else {
//             providentFundResponse = await calculateCurrentProvidentFundDetails(
//                 employeePfWage,
//                 providentFundSettings,
//                 providentFundDetails
//             );
//         }

//         return {
//             'Retirals_Id': providentFundResponse.Retirals_Id || providentFundDetails.Retirals_Id,
//             'Form_Id': providentFundResponse.Form_Id || providentFundDetails.Form_Id,
//             'Employee_Retiral_Wages': providentFundResponse.Employee_Retiral_Wages,
//             'Employer_Retiral_Wages': providentFundResponse.Employer_Retiral_Wages,
//             'Employee_Share_Amount': providentFundResponse.Employee_Share_Amount,
//             'Employer_Share_Amount': providentFundResponse.Employer_Share_Amount,
//             'Employer_Share_Percentage': providentFundDetails.Employer_Share_Percentage,
//             'Employee_Share_Percentage': providentFundDetails.Employee_Share_Percentage,
//             'Admin_Charge': providentFundResponse.Admin_Edli_Charges?.Employee_Admin_Charge || 0,
//             'EDLI_Charge': providentFundResponse.Admin_Edli_Charges?.Employee_Edli_Charge || 0,
//             'Retiral_Type': providentFundDetails?.Retirals_Type
//         };
//     } catch (error) {
//         console.error('Error processing provident fund details:', error);
//         throw error;
//     }
// }

/**
 * Calculates bonus details based on the given allowance details and basic pay.
 * @param {Array} bonusAllowanceDetails - Array of bonus allowance details.
 * @param {Array} allowanceDetails - Array of allowance details.
 * @param {number} basicPay - Basic pay amount.
 * @returns {Array} - Array of bonus details.
 */
function calculateBonusDetails(bonusAllowanceDetails, allowanceDetails, basicPay) {
    try {
        let period = {
            'Quarterly': 3,
            'HalfYearly': 6,
            'Monthly': 1,
            'Annually': 12
        }
        const bonusFormId = formId.bonus.toString();
        const bonusWages = getStatutorySalary(allowanceDetails, basicPay, bonusFormId);

        if (bonusAllowanceDetails?.length > 0) {
            for (bonus of bonusAllowanceDetails) {
                bonus.Amount = calculateAllowanceAmount(bonusWages, bonus);
            }
        }

        let formAllowanceData = bonusAllowanceDetails.map((item) => {
            return {
                Allowance_Type_Id: item.Allowance_Type_Id,
                Allowance_Type: item.Allowance_Type,
                Percentage: item.Percentage,
                Amount: item.Amount,
                Period: item.Period,
                Allowance_Wage: item.Allowance_Type?.toLowerCase() === 'percentage' ? bonusWages*period[item.Period] : null
            }
        })
        return formAllowanceData;
    }
    catch (error) {
        console.error('Error in calculateBonusDetails function', error);
        throw error;
    }
}

/**
 * Calculates allowance details based on the given allowance details and basic pay.
 * @param {Array} allowanceDetails - Array of allowance details.
 * @param {number} basicPay - Basic pay amount.
 * @returns {Array} - Array of allowance details.
 */
function calculateAllowanceDetails(allowanceDetails, basicPay) {
    try {
        allowanceDetails = allowanceDetails.filter((item) => item.Allowance_Mode?.toLowerCase() === 'non bonus' && item.Component_Code?.toLowerCase() !== 'basic_salary_amount')
        if (allowanceDetails?.length > 0) {
            for (allowance of allowanceDetails) {
                //We will calculate the Formula Based allowance at the end
                if (allowance.Component_Code?.toLowerCase() === 'fixed_allowance_amount') {
                    allowance.Amount = 0;
                    allowance.Allowance_Wage = 0;
                } else {
                    if (allowance.Allowance_Type?.toLowerCase() === 'percentage') {
                        let allowanceAmount = (basicPay * parseFloat(allowance.Percentage)) / 100;
                        allowance.Amount = getRoundOffValue(formId.salary, allowanceAmount);
                        allowance.Allowance_Wage = basicPay;
                    } else {
                        allowance.Allowance_Wage = 0;
                    }
                    //For Allowance Type is Amount it will be as it is
                }
            }
        }
        return allowanceDetails;
    }
    catch (error) {
        console.error('Error in formAllowanceData:', error);
        throw error;
    }
}

/**
 * Processes insurance details and calculates employee and employer contributions
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} insuranceDetails - Array of insurance details
 * @param {Array} allowanceDetails - Array of allowance details
 * @param {number} basicPay - Employee's basic pay
 * @param {Object} salaryDetails - Salary details object
 * @param {boolean} revisionWithoutArrear - Flag indicating revision without arrear
 * @returns {Promise<Array>} - Array of processed insurance details
 */
async function calculateInsuranceDetails(
    organizationDbConnection,
    insuranceTypes,
    allowanceDetails,
    basicPay,
    salaryDetails,
    revisionWithoutArrear = false
) {
    try {
        const employeeRetiralDetails = [];

        const fixedInsurance = insuranceTypes.filter(i => i.Insurance_Type?.toLowerCase() === 'fixed');
        const variableInsurance = insuranceTypes.filter(i => i.Insurance_Type?.toLowerCase() === 'variable');
        const slabWiseInsurance = insuranceTypes.filter(i => i.Slab_Wise_Insurance?.toLowerCase() === 'yes');

        let insuranceWages = (variableInsurance.length > 0 || slabWiseInsurance.length > 0)
            ? getStatutorySalary(allowanceDetails, basicPay, formId.variableInsurance.toString())
            : 0;
        const createInsuranceDetail = ({
            InsuranceType_Id,
            Form_Id,
            Insurance_Name,
            Employee_Retiral_Wages,
            Employer_Retiral_Wages,
            Employee_Share_Amount,
            Employer_Share_Amount,
            Employee_Share_Percentage = null,
            Employer_Share_Percentage = null,
            Retiral_Type,
            Payment_Frequency,
            Contribution_End_Month = null,
            Contribution_Period_Completed = null

        }) => ({
            Retirals_Id: InsuranceType_Id,
            Form_Id,
            Insurance_Name,
            Employee_Retiral_Wages,
            Employer_Retiral_Wages,
            Employee_Share_Amount: getInsuranceSpecificRoundOffValue(
                InsuranceType_Id,
                Employee_Share_Amount,
                insuranceRoundOffMap
            ),
            Employer_Share_Amount: getInsuranceSpecificRoundOffValue(
                InsuranceType_Id,
                Employer_Share_Amount,
                insuranceRoundOffMap
            ),
            Employee_Share_Percentage,
            Employer_Share_Percentage,
            Retiral_Type,
            Payment_Frequency,
            Contribution_End_Month,
            Contribution_Period_Completed
        });

        // Fixed insurance processing
        for (const fixed of fixedInsurance) {
            employeeRetiralDetails.push(createInsuranceDetail({
                ...fixed,
                Employee_Retiral_Wages: null,
                Employer_Retiral_Wages: null,
                Employee_Share_Amount: fixed.Employee_Share_Amount,
                Employer_Share_Amount: fixed.Employer_Share_Amount,
                Retiral_Type: 'Fixed',
                Payment_Frequency: fixed.Payment_Frequency
            }));
        }
        // Variable insurance processing
        for (const variable of variableInsurance) {
            const esiEligible = variable.Employee_State_Insurance?.toLowerCase() === 'yes';
            let includeVariable = true;

            const empShare = (variable.Employee_Share_Percentage * insuranceWages) / 100;
            const emrShare = (variable.Employer_Share_Percentage * insuranceWages) / 100;

            let finalInsuranceWages = insuranceWages - emrShare;
            if (esiEligible) {
                const config = await retrieveInsuranceContributionConfiguration(organizationDbConnection);
                const existingCondition  = (config && finalInsuranceWages >= config.Min_Salary && finalInsuranceWages <= config.Max_Salary) ||
                    (salaryDetails.Effective_From && salaryDetails.ESI_Contribution_End_Date);

                // NEW CONDITION: ESI continuation after revision (only if flag is not 'Yes')
                let newCondition = false;

                if (variable.Contribution_Period_Completed !== 'Yes') {
                    // Check if we need to calculate contribution end month
                    if (config && revisionWithoutArrear &&
                        !variable.Contribution_End_Month &&
                        finalInsuranceWages > config.Max_Salary &&
                        salaryDetails.Salary_Effective_Month) {
                        // Calculate contribution end month using Salary_Effective_Month (format: "M,YYYY")
                        const contributionEndMonth = await calculateContributionEndMonth(
                            organizationDbConnection,
                            salaryDetails.Salary_Effective_Month
                        );
                        if (contributionEndMonth) {
                            // Set the contribution end month and flag (format: "M,YYYY")
                            variable.Contribution_End_Month = contributionEndMonth;
                            variable.Contribution_Period_Completed = 'No';
                        }
                    }

                    // If contribution end month is set, new condition is true
                    if (variable.Contribution_End_Month) {
                        newCondition = true;
                    }
                }

                // FINAL DECISION: Include ESI if EITHER condition is true
                includeVariable = existingCondition || newCondition;
            }

            if (includeVariable) {
                employeeRetiralDetails.push(createInsuranceDetail({
                    ...variable,
                    Employee_Retiral_Wages: insuranceWages,
                    Employer_Retiral_Wages: insuranceWages,
                    Employee_Share_Amount: empShare,
                    Employer_Share_Amount: emrShare,
                    Employee_Share_Percentage: variable.Employee_Share_Percentage,
                    Employer_Share_Percentage: variable.Employer_Share_Percentage,
                    Retiral_Type: 'Variable',
                    Contribution_End_Month: variable.Contribution_End_Month || null,
                    Contribution_Period_Completed: variable.Contribution_Period_Completed || null
                }));

            }
        }

        // Slab-wise insurance processing
        if (slabWiseInsurance.length > 0) {
            const philHealthSlabs = await organizationDbConnection(ehrTables.philHealthSlabs).select('*');

            for (const slab of slabWiseInsurance) {
                const slabDetails = calculateSlabWiseInsurance(philHealthSlabs, insuranceWages, slab.InsuranceType_Id);
                if (!slabDetails) continue;

                employeeRetiralDetails.push(createInsuranceDetail({
                    ...slab,
                    Employee_Retiral_Wages: insuranceWages,
                    Employer_Retiral_Wages: insuranceWages,
                    Employee_Share_Amount: slabDetails.Employee_Share_Amount,
                    Employer_Share_Amount: slabDetails.Employer_Share_Amount,
                    Employee_Share_Percentage: slabDetails.Employee_Share_Percentage,
                    Employer_Share_Percentage: slabDetails.Employer_Share_Percentage,
                    Retiral_Type: 'Variable'
                }));
            }
        }

        return employeeRetiralDetails;
    } catch (error) {
        console.error('Error in calculateInsuranceDetails:', error);
        throw error;
    }
}


/**
 * Retrieves the insurance contribution configuration for a given insurance type
 * @param {Knex} organizationDbConnection - Knex connection to the organization database
 * @param {number} insuranceTypeId - ID of the insurance type to retrieve the configuration for
 * @returns {Object} Insurance contribution configuration object
 */
async function retrieveInsuranceContributionConfiguration(organizationDbConnection) {
    try {
        let insuranceContributionConfiguration = await organizationDbConnection(ehrTables.esiStatutoryConfiguration)
            .select('*')
            .first()

        return insuranceContributionConfiguration
    } catch (error) {
        console.error('Error in retrieveInsuranceContributionConfiguration', error);
        throw error
    }
}

/**
 * Retrieves the social security scheme details
 * @param {Knex} organizationDbConnection - Knex connection to the organization database
 * @returns {Array} Array of social security scheme details
 */
async function retrieveSocialSecurityScheme(organizationDbConnection) {
    try {
        let socialSecurityScheme = await organizationDbConnection(ehrTables.socialSecurityScheme)
            .select('*')

        return socialSecurityScheme
    } catch (err) {
        console.error('Error in retrieveSocialSecurityScheme', err);
        throw err
    }
}

/**
 * Retrieves the provident fund settings from the database for a given organization.
 * @param {Knex} organizationDbConnection - Knex connection to the organization's database.
 * @returns {Promise<Object>} - Promise resolving to the provident fund settings.
 * @throws Will throw an error if the database query fails.
 */
async function retrieveProvidentFundSettings(organizationDbConnection) {
    try {
        let providentFundSettings = await organizationDbConnection(ehrTables.providentFundSettings)
            .select('*')
            .first()

        return providentFundSettings
    } catch (err) {
        console.error('Error in retrieveProvidentFundSettings', err);
        throw err
    }
}

/**
 * Calculates slab-wise Provident Fund (PF) contributions based on employee's PF wage and social security scheme slabs
 * @param {Array} socialSecuritySchemeSlabs - Array of social security scheme slabs
 * @param {number} employeePfWage - Employee's PF wage
 * @returns {Object} - Object containing PF contribution details
 */
async function calculateSlabWisePf(socialSecuritySchemeSlabs, employeePfWage) {
    try {
        // Round off the employee's PF wage
        employeePfWage = getRoundOffValue(formId.pfId, employeePfWage);

        // Initialize default return values
        const providentFundDetails = {};
        const adminEdliCharges = {
            Employee_Admin_Charge: 0,
            Employee_Edli_Charge: 0
        };

        // Find the matching slab
        for (const slab of socialSecuritySchemeSlabs) {
            const rangeFrom = parseFloat(slab.Range_From) || 0;
            let rangeTo = parseFloat(slab.Range_To) || 9999999999999.99;

            if (employeePfWage >= rangeFrom && employeePfWage <= rangeTo) {
                // Parse slab values with defaults
                const medianValue = parseFloat(slab.Median_Value) || 0;
                const wisp = parseFloat(slab.WISP) || 0;

                // Parse percentages
                const regularSSEEPercentage = parseFloat(slab.Regular_SS_EE_Percentage) || 0;
                const wispEEPercentage = parseFloat(slab.WISP_EE_Percentage) || 0;
                const regularSSERPercentage = parseFloat(slab.Regular_SS_ER_Percentage) || 0;
                const wispERPercentage = parseFloat(slab.WISP_ER_Percentage) || 0;
                const ecEE = parseFloat(slab.EC_EE) || 0;
                const ecER = parseFloat(slab.EC_ER) || 0;

                // Calculate employee contributions
                const regularSSEE = (medianValue * regularSSEEPercentage) / 100;
                const wispEmployeeShare = (wisp * wispEEPercentage) / 100;
                const sumofEmployeeShare = regularSSEE + wispEmployeeShare + ecEE;

                // Calculate employer contributions
                const regularSSER = (medianValue * regularSSERPercentage) / 100;
                const wispEmployerShare = (wisp * wispERPercentage) / 100;
                const sumofEmployerShare = regularSSER + wispEmployerShare + ecER;

                // Build the result object with rounded values
                return {
                    Retirals_Id: providentFundDetails.Retirals_Id,
                    Form_Id: providentFundDetails.Form_Id,
                    Regular_SS_EE: getRoundOffValue(formId.pfId, regularSSEE),
                    WISP_EE: getRoundOffValue(formId.pfId, wispEmployeeShare),
                    EC_EE: getRoundOffValue(formId.pfId, ecEE),
                    Employee_Share_Amount: getRoundOffValue(formId.pfId, sumofEmployeeShare),
                    Regular_SS_ER: getRoundOffValue(formId.pfId, regularSSER),
                    WISP_ER: getRoundOffValue(formId.pfId, wispEmployerShare),
                    EC_ER: getRoundOffValue(formId.pfId, ecER),
                    Employer_Share_Amount: getRoundOffValue(formId.pfId, sumofEmployerShare),
                    Employee_Provident_Fund_Wage: employeePfWage,
                    Admin_Edli_Charges: {
                        Employee_Admin_Charge: 0,
                        Employee_Edli_Charge: 0
                    }
                };
            }
        }

        // Return empty values if no matching slab is found
        return {
            Regular_SS_EE: 0,
            WISP_EE: 0,
            EC_EE: 0,
            Employee_Share_Amount: 0,
            Regular_SS_ER: 0,
            WISP_ER: 0,
            EC_ER: 0,
            Employer_Share_Amount: 0,
            Employee_Provident_Fund_Wage: 0,
            Admin_Edli_Charges: adminEdliCharges
        };
    } catch (error) {
        console.error('Error in calculateSlabWisePf', error);
        throw error
    }
}


/**
 * Calculates the Provident Fund salary based on various conditions
 * @param {number} basicPay - Employee's basic pay
 * @param {Array} allowanceDetails - Array of allowance details
 * @param {Object} providentFundSettings - PF settings from database
 * @param {Object} providentFundDetails - Employee's PF details
 * @param {number} actualBasicPay - Employee's actual basic pay
 * @param {number} pfAdhocAllowanceAmount - Additional adhoc allowance amount (default: 0)
 * @returns {number} - Calculated PF salary
 */
function getPfSalary(basicPay, allowanceDetails, providentFundSettings, providentFundDetails, actualBasicPay, pfAdhocAllowanceAmount = 0, Provident_Fund_Configuration_Value) {
    try {
        let basicPlusAllowance = basicPay + (parseFloat(pfAdhocAllowanceAmount) || 0);
        const benefitFormId = formId.pfId.toString();
        const restrictedWage = providentFundSettings?.Restricted_PF_Wage_Amount || 15000;
        // Check if we should return basic + allowance immediately
        if (Provident_Fund_Configuration_Value?.toLowerCase() === 'current' &&
            actualBasicPay >= restrictedWage &&
            providentFundDetails?.PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit?.toLowerCase() === 'yes') {
            return basicPlusAllowance;
        }

        // Process allowances based on configuration
        if (allowanceDetails?.length > 0) {
            // First pass: Process allowances based on current configuration
            for (const allowance of allowanceDetails) {
                if (allowance?.BenefitForms?.split(',').includes(benefitFormId)) {
                    // Include allowance if:
                    // 1. Consider_For_EPF_Contribution is 'always', OR
                    // 2. It's a formula-based allowance (Fixed Allowance should always be included in PF wages)
                    const shouldInclude = allowance.Consider_For_EPF_Contribution?.toLowerCase() === 'always' ||
                                        allowance.Component_Code?.toLowerCase() === 'fixed_allowance_amount';

                    if (shouldInclude) {
                        basicPlusAllowance += parseFloat(allowance.Amount || 0);
                    }
                }
            }

            // Second pass: Process LOP conditions if applicable
            if (providentFundDetails?.Provident_Fund_Configuration?.toLowerCase() === 'current' &&
                providentFundDetails?.Consider_All_Salary_Components_For_LOP?.toLowerCase() === 'yes') {

                for (const allowance of allowanceDetails) {
                    if (allowance?.BenefitForms?.split(',').includes(benefitFormId) &&
                        allowance.Consider_For_EPF_Contribution?.toLowerCase() === 'only when pf wage is less than ₹15,000' &&
                        basicPlusAllowance < restrictedWage) {

                        basicPlusAllowance += parseFloat(allowance.Amount || 0);
                        if (basicPlusAllowance > restrictedWage) {
                            basicPlusAllowance = restrictedWage;
                        }
                    }
                }
            }
        }

        return basicPlusAllowance;
    } catch (error) {
        console.error('Error in getPfSalary', error);
        throw error
    }
}

/**
 * Calculates the Provident Fund wage based on contribution rate
 * @param {string} employeeContributionRate - Employee's contribution rate setting
 * @param {string} employerContributionRate - Employer's contribution rate setting
 * @param {number} employeePfWage - Employee's PF wage
 * @param {Object} providentFundSettings - PF settings from database
 * @returns {Object} Object containing Employee_Pf_Wage and Employer_Pf_Wage
 */
function getProvidentFundWageBasedOnContributionRate(employeeContributionRate, employerContributionRate, employeePfWage, providentFundSettings) {
    try {
        let employeeWage = employeePfWage;
        let employerWage = employeePfWage;

        // Calculate employee wage based on contribution rate
        if (employeeContributionRate?.toLowerCase() === 'restrict') {
            employeeWage = Math.min(employeePfWage, providentFundSettings.Restricted_PF_Wage_Amount || 15000);
        }

        // Calculate employer wage based on contribution rate
        if (employerContributionRate?.toLowerCase() === 'restrict') {
            employerWage = Math.min(employeePfWage, providentFundSettings.Restricted_PF_Wage_Amount || 15000);
        }

        return {
            Employee_Pf_Wage: employeeWage,
            Employer_Pf_Wage: employerWage
        };
    } catch (error) {
        console.error('Error in getProvidentFundWageBasedOnContributionRate', error);
        throw error
    }
}

/**
 * Calculates current Provident Fund details including employee and employer contributions
 * @param {number} employeePfWage - Employee's PF wage
 * @param {Object} providentFundSettings - PF settings from database
 * @param {Object} providentFundDetails - Employee's PF details
 * @returns {Object} Object containing PF details including shares and charges
 */
async function calculateCurrentProvidentFundDetails(employeePfWage, providentFundSettings, providentFundDetails) {
    try {

        if (providentFundDetails?.Retirals_Type?.toLowerCase() === 'fixed') {
            return {
                Employer_Share_Amount: providentFundDetails.Employer_Share_Amount,
                Employee_Share_Amount: providentFundDetails.Employee_Share_Amount,
                Admin_Edli_Charges: {
                    Admin_Edli_Charges: 0,
                    Admin_Edli_Charges_Amount: 0
                },
                Employee_Provident_Fund_Wage: null,
                Retirals_Id: providentFundDetails.Retirals_Id,
                Employee_Retiral_Wages: null,
                Employer_Retiral_Wages: null,
                Form_Id: providentFundDetails.Form_Id,
            };
        } else {
            // Get wages based on contribution rates
            const pfWageBasedOnContributionRate = getProvidentFundWageBasedOnContributionRate(
                providentFundDetails?.PF_Employee_Contribution,
                providentFundDetails?.PF_Employer_Contribution,
                employeePfWage,
                providentFundSettings
            );

            const { Employee_Pf_Wage: employeeWage, Employer_Pf_Wage: employerWage } = pfWageBasedOnContributionRate;

            // Calculate base PF contributions
            let employeeShareAmount = (employeeWage * (providentFundDetails.Employee_Share_Percentage || 12)) / 100;
            let employerShareAmount = (employerWage * (providentFundDetails.Employer_Share_Percentage || 12)) / 100;

            // Round off the amounts
            employeeShareAmount = getRoundOffValue(formId.pfId, employeeShareAmount);
            employerShareAmount = getRoundOffValue(formId.pfId, employerShareAmount);

            // Calculate admin charge if applicable
            let adminChargeAmount = 0;
            if (providentFundDetails.Admin_Charge_Part_Of_CTC?.toLowerCase() === 'yes') {
                const adminCharge = parseFloat(providentFundSettings.Admin_Charge) || 0;
                const adminChargeMaxAmount = parseFloat(providentFundSettings.Admin_Charge_Max_Amount) || Infinity;
                adminChargeAmount = getRoundOffValue(formId.pfId, Math.min(employeeWage * (adminCharge / 100), adminChargeMaxAmount));
            }

            // Calculate EDLI charge if applicable
            let edliChargeAmount = 0;
            if (providentFundDetails.Edli_Charge_Part_Of_CTC?.toLowerCase() === 'yes') {
                const edliConfigurationEmployer = parseFloat(providentFundSettings.EDLI_Charge) || 0;
                const edliChargeMaxAmount = parseFloat(providentFundSettings.EDLI_Charge_Max_Amount) || Infinity;
                edliChargeAmount = getRoundOffValue(formId.pfId, Math.min(employeeWage * (edliConfigurationEmployer / 100), edliChargeMaxAmount));
            }

            // Prepare and return the result
            const adminEdliCharges = {
                Employee_Admin_Charge: adminChargeAmount,
                Employee_Edli_Charge: edliChargeAmount
            };
            return {
                Employer_Share_Amount: employerShareAmount,
                Employee_Share_Amount: employeeShareAmount,
                Admin_Edli_Charges: adminEdliCharges,
                Employee_Provident_Fund_Wage: employeeWage,
                Retirals_Id: providentFundDetails.Retirals_Id,
                Employee_Retiral_Wages: employeeWage,
                Employer_Retiral_Wages: employerWage,
                Form_Id: providentFundDetails.Form_Id,
            };
        }


    } catch (error) {
        console.error('Error in calculateCurrentProvidentFundDetails:', error);
        throw error;
    }
}

/**
 * Retrieves gratuity settings from database
 * @param {Object} organizationDbConnection - Database connection
 * @returns {Promise<Object>} - Gratuity settings, or null if not found
 */
async function retrieveGratuitySettings(organizationDbConnection) {
    try {
        const gratuitySettings = await organizationDbConnection(ehrTables.gratuitySettings)
            .select('Working_Days', 'Org_Salary_Days', 'Part_Of_GratuityAct')
            .first();

        if (!gratuitySettings) {
            console.log('No gratuity settings found');
            return null;
        }

        return gratuitySettings;
    } catch (error) {
        console.error('Error in retrieveGratuitySettings:', error);
        throw error;
    }
}

/**
 * Calculates gratuity amount based on basic pay and organization settings
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} basicPay - Employee's basic pay
 * @param {Object} gratuityDetails - Gratuity details
 * @param {Array} allowanceDetails - Array of allowance details
 * @returns {Promise<number>} - Calculated gratuity amount
 */
async function calculateGratuityAmount(basicPay, gratuityDetails, allowanceDetails, gratuitySettings) {
    try {
        const gratuityFormId = formId.gratuityId.toString();
        // Calculate gratuity enabled allowance
        const gratuityEnabledAllowance = getStatutorySalary(allowanceDetails, basicPay, gratuityFormId);
        // Calculate gratuity amount
        const gratuityAmount = gratuityEnabledAllowance *
            (gratuitySettings.Org_Salary_Days / gratuitySettings.Working_Days)

        return {
            'Retirals_Id': gratuityDetails.Retirals_Id,
            'Form_Id': formId.gratuityId,
            'Employee_Retiral_Wages': gratuityEnabledAllowance,
            'Employer_Retiral_Wages': gratuityEnabledAllowance,
            'Employee_Share_Amount': 0,
            'Employer_Share_Amount': getRoundOffValue(formId.gratuityId,gratuityAmount),
            'Employer_Share_Percentage': null,
            'Employee_Share_Percentage': null,
            'Retiral_Type': 'Fixed'
        }

    } catch (error) {
        console.error('Error in calculateGratuityAmount:', error);
        throw error;
    }
}
/**
 * Retrieves the payroll general settings from the database for a given organization.
 * @param {Object} organizationDbConnection - Database connection object for the organization's database.
 * @returns {Promise<Object>} - Promise resolving to the payroll general settings.
 * @throws Will throw an error if the database query fails.
 */
async function retrievePayrollGeneralSettings(organizationDbConnection) {
    try {
        let payrollGeneralSettings = await organizationDbConnection(ehrTables.payrollGeneralSettings)
            .select('*')
            .first()

        return payrollGeneralSettings
    } catch (err) {
        console.error('Error in retrievePayrollGeneralSettings', err);
        throw err
    }
}

/**
 * Retrieves salary configuration details for a given employee from the database.
 * For candidates, returns hardcoded default configuration.
 *
 * @param {Object} organizationDbConnection - Knex connection to the organization's database.
 * @param {number|string} employeeId - ID of the employee whose salary configuration is to be retrieved.
 * @param {number|string} candidateId - ID of the candidate (if present, indicates candidate calculation).
 * @returns {Promise<Object>} - Promise resolving to an object containing the salary configuration details.
 * @throws Will throw an error if the database query fails.
 */
async function retrieveSalaryConfiguration(organizationDbConnection, employeeId, candidateId) {
    try {
        // For candidates, return hardcoded default configuration
        if (candidateId) {
            return {
                'Eligible_For_Insurance': 1,
                'Eligible_For_Gratuity': 1,
                'Eligible_For_Pf': 1,
                'Eligible_For_Nps': 1
            };
        }

        if(employeeId){
            let salaryDetails = await organizationDbConnection(ehrTables.employeeSalaryConfiguration)
                .select('*')
                .where('Employee_Id', employeeId)
                .first()

            return salaryDetails
        }

        let salaryDetails = {
            'Eligible_For_Insurance': 1,
            'Eligible_For_Gratuity': 1,
            'Eligible_For_Pf': 1,
            'Eligible_For_Nps': 1
        }

        return salaryDetails
    } catch (err) {
        console.error('Error in retrieveSalaryConfiguration', err);
        throw err
    }
}


/**
 * Gets allowance details for specific allowance type IDs with optional benefit association
 * @param {Object} organizationDbConnection - Database connection object
 * @param {Array} allowance - Array of allowance objects with Allowance_Type_Id
 * @returns {Promise<Object>} - Promise resolving to allowance details
 */
async function getAllowanceDetails(organizationDbConnection, allowance) {
    try {
        let allowanceDetails = await organizationDbConnection('allowance_type as AT')
            .select([
                'AT.Allowance_Type_Id', 'AT.Allowance_Percentage as Percentage', 'AT.Allowance_Amount as Amount',
                'AT.Allowance_As_Is_Payment as As_Is_Payment', 'AT.FBP_Max_Declaration_Amount as FBP_Max_Declaration', 'AT.Calculation_Type as Allowance_Type',
                organizationDbConnection.raw('GROUP_CONCAT(BF.Form_Id) as BenefitForms'),
                'AT.Allowance_Name', 'AT.Workflow_Id',
                'AT.Tax_Inclusion', 'AT.Period',
                'AT.Allowance_Mode', 'AT.Is_Claim_From_Reimbursement',
                'AT.Consider_For_EPF_Contribution', 'AT.Is_Flexi_Benefit_Plan',
                'AT.Perquisites_Id', 'SC.Component_Code'
            ])
            .leftJoin('allowance_type_benefit_association as ABA', 'ABA.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .leftJoin('benefit_forms as BF', 'BF.Form_Id', 'ABA.Form_Id')
            .leftJoin('salary_components as SC', 'AT.Salary_Component_Id', 'SC.Component_Id')
            // .where('AT.AllowanceType_Status', 'Active')
            .groupBy('AT.Allowance_Type_Id')
            .whereIn('AT.Allowance_Type_Id', allowance.map((item) => item.Allowance_Type_Id))
            .whereNotNull('AT.Calculation_Type');

        // Create a map using Allowance_Type_Id from database format
        let userAllowanceData = {};
        allowance.forEach(item => {
            userAllowanceData[item.Allowance_Type_Id] = item;
        });
        for (let updatedAllowance of allowanceDetails) {
            // Match using Allowance_Type_Id from database query results
            let currentRecord = userAllowanceData[updatedAllowance.Allowance_Type_Id];
            if (currentRecord) {
                updatedAllowance.Amount = currentRecord.Amount;
                updatedAllowance.Percentage = currentRecord.Percentage;
                updatedAllowance.FBP_Max_Declaration = currentRecord.FBP_Max_Declaration || updatedAllowance.FBP_Max_Declaration;
            }
        }

        return allowanceDetails;
    } catch (error) {
        console.error('Error in getAllowanceDetails:', error);
        throw error;
    }
}

/**
 * Calculates insurance contributions based on salary slabs
 * @param {Array} insuranceSlabDetails - Array of insurance slab details
 * @param {number} employeeInsuranceWage - Employee's wage for insurance calculation
 * @param {number} insuranceTypeId - Insurance Type ID for specific round-off
 * @returns {Object} - Object containing employee and employer share amounts
 */
function calculateSlabWiseInsurance(insuranceSlabDetails, employeeInsuranceWage, insuranceTypeId = null) {
    try {
        const insuranceDetails = {};

        if (!insuranceSlabDetails || !Array.isArray(insuranceSlabDetails)) {
            return insuranceDetails;
        }

        for (const insuranceSlab of insuranceSlabDetails) {
            const rangeTo = insuranceSlab.Range_To || '9999999999999.99';
            const rangeFrom = parseFloat(insuranceSlab.Range_From) || 0;
            const wage = parseFloat(employeeInsuranceWage) || 0;

            if (rangeFrom <= wage && parseFloat(rangeTo) >= wage) {
                const salaryLimit = insuranceSlab.Salary_Limit;
                const cappedValue = parseFloat(insuranceSlab.Capped_Value) || 0;
                const employeeShare = parseFloat(insuranceSlab.Employee_Share) || 0;
                const employerShare = parseFloat(insuranceSlab.Employer_Share) || 0;

                let employeeShareAmount, employerShareAmount;

                if (salaryLimit?.toLowerCase() === 'actual') {
                    employeeShareAmount = (wage * employeeShare) / 100;
                    employerShareAmount = (wage * employerShare) / 100;
                } else {
                    employeeShareAmount = (cappedValue * employeeShare) / 100;
                    employerShareAmount = (cappedValue * employerShare) / 100;
                }

                // Use insurance-specific round-off if insuranceTypeId is provided
                if (insuranceTypeId) {
                    insuranceDetails.Employee_Share_Amount = getInsuranceSpecificRoundOffValue(
                        insuranceTypeId,
                        employeeShareAmount,
                        insuranceRoundOffMap
                    );
                    insuranceDetails.Employer_Share_Amount = getInsuranceSpecificRoundOffValue(
                        insuranceTypeId,
                        employerShareAmount,
                        insuranceRoundOffMap
                    );
                } else {
                    // Fallback to default round-off
                    insuranceDetails.Employee_Share_Amount = getRoundOffValue(formId.pfId, employeeShareAmount);
                    insuranceDetails.Employer_Share_Amount = getRoundOffValue(formId.pfId, employerShareAmount);
                }

                return insuranceDetails;
            }
        }

        return insuranceDetails;
    } catch (error) {
        console.error('Error in calculateSlabWiseInsurance:', error);
        throw error;
    }
}

/**
 * Rounds off a value based on the specified rounding settings
 * @param {string} roundOffFor - The type of value to round off (e.g., 'EPF', 'ESI', etc.)
 * @param {number} value - The value to be rounded
 * @param {Object} [roundOffSettings=null] - Optional round off settings object
 * @param {number} [semiValue=2] - Multiplier for decimal rounding (default: 2)
 * @returns {Promise<number>} - The rounded value
 */
function getRoundOffValue(roundOffFor, value, semiValue = 2) {
    try {
        let roundOffSetting = roundOffSettings.find((item) => item.Form_Id == roundOffFor);
        if (roundOffSetting) {
            const multiplesOf = parseFloat(roundOffSetting.Multiples_Of) || 0;
            const roundOffSettingId = parseInt(roundOffSetting.Round_Off_Settings_Id) || 0;

            if (multiplesOf === 0.5) {
                switch (roundOffSettingId) {
                    case 1: // Round to nearest 0.5 or 1
                        return Math.round(value * semiValue) / semiValue;
                    case 2: // Round up to next 0.5 or 1
                        return Math.ceil(value * semiValue) / semiValue;
                    case 3: // Round down to previous 0.5 or 1
                        return Math.floor(value * semiValue) / semiValue;
                    default: // No rounding
                        return parseFloat(Number(value).toFixed(2));
                }
            } else {
                switch (roundOffSettingId) {
                    case 1: // Round to nearest integer
                        return Math.round(value);
                    case 2: // Round up to next integer
                        return Math.ceil(value);
                    case 3: // Round down to previous integer
                        return Math.floor(value);
                    default: // No rounding
                        return parseFloat(Number(value).toFixed(2));
                }
            }
        }

        return value;
    } catch (error) {
        console.error('Error in getRoundOffValue:', error);
        throw error;
    }
}

/**
 * Retrieves round off settings from the database
 * @param {string} roundOffFor - The type of value to get settings for
 * @returns {Promise<Object>} - The round off settings
 */
async function getRoundOffSettings() {
    try {
        const roundOffSettings = await organizationDbConnection(ehrTables.payrollRoundOffSettings)
            .select('Round_Off_Settings_Id', 'Multiples_Of', 'Round_Off_For', 'Form_Id')
        return roundOffSettings || [];
    } catch (error) {
        console.error('Error in getRoundOffSettings:', error);
        throw error;
    }
}

/**
 * Calculates allowance amount based on given basic pay and allowance details.
 * @param {number} basicPay - Basic pay amount.
 * @param {Object} allowance - Allowance details.
 * @returns {number} - Calculated allowance amount.
 */
function calculateAllowanceAmount(basicPay, allowance) {
    try {
        const period = {
            'Quarterly': 3,
            'HalfYearly': 6,
            'Monthly': 1,
            'Annually': 12
        };
        let allowanceAmount = 0;
        if (allowance.Percentage !== null && allowance.Percentage !== undefined) {
            allowanceAmount = (basicPay * (allowance.Percentage / 100))*period[allowance.Period];
        } else {
            allowanceAmount = allowance.Amount;
        }     

        return getRoundOffValue(formId.salary, allowanceAmount);
    } catch (error) {
        console.error('Error in calculateAllowanceAmount:', error);
        throw error;
    }
}
function ensureArray(result, key) {
    return result[key] || [];
}
function isMonthYearInPeriodArray(month, year, periodArray) {
    if (!periodArray || !Array.isArray(periodArray) || periodArray.length === 0) {
        return false;
    }

    // Iterate through array in pairs [month, year]
    for (let i = 0; i < periodArray.length; i += 2) {
        const periodMonth = periodArray[i];
        const periodYear = periodArray[i + 1];

        if (periodMonth === month && periodYear === year) {
            return true;
        }
    }

    return false;
}

/**
 * Get ESI contribution periods based on configuration with financial year
 * @param {Knex} organizationDbConnection - Database connection
 * @returns {Object} - Object containing first and second contribution periods with years
 */
async function getContributionPeriods(organizationDbConnection) {
    try {
        // Get ESI configuration and Assessment Year from org_details
        const [esiConfig, orgDetails] = await Promise.all([
            organizationDbConnection(ehrTables.esiStatutoryConfiguration)
                .select('Start_Month_Id', 'Contribution_Period')
                .first(),
            organizationDbConnection('org_details')
                .select('Assessment_Year')
                .first()
        ]);

        if (!esiConfig || !esiConfig.Start_Month_Id || !esiConfig.Contribution_Period) {
            return null;
        }

        if (!orgDetails || !orgDetails.Assessment_Year) {
            return null;
        }

        const startMonth = esiConfig.Start_Month_Id; // e.g., 4 for April
        const periodDuration = esiConfig.Contribution_Period; // e.g., 6 months
        const assessmentYear = orgDetails.Assessment_Year; // e.g., 2026
        const financialStartYear = assessmentYear - 1; // e.g., 2025

        // Build first period array [month, year, month, year, ...]
        const firstPeriodArray = [];
        let currentMonth = startMonth;
        let currentYear = financialStartYear;

        for (let i = 0; i < periodDuration; i++) {
            firstPeriodArray.push(currentMonth, currentYear);
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
        }

        // Build second period array [month, year, month, year, ...]
        const secondPeriodArray = [];
        // Second period starts after first period ends
        for (let i = 0; i < periodDuration; i++) {
            secondPeriodArray.push(currentMonth, currentYear);
            currentMonth++;
            if (currentMonth > 12) {
                currentMonth = 1;
                currentYear++;
            }
        }

        return {
            firstPeriod: firstPeriodArray,
            secondPeriod: secondPeriodArray
        };
    } catch (error) {
        console.error('Error in getContributionPeriods:', error);
        return null;
    }
}

/**
 * Calculate contribution end month for ESI
 * @param {Knex} organizationDbConnection - Database connection
 * @param {string} salaryEffectiveMonth - Salary effective month in "M,YYYY" format (e.g., "7,2025")
 * @returns {string|null} - Contribution end month in "M,YYYY" format or null
 */
async function calculateContributionEndMonth(organizationDbConnection, salaryEffectiveMonth) {
    try {
        if (!salaryEffectiveMonth) {
            return null;
        }

        // Get contribution periods
        const periods = await getContributionPeriods(organizationDbConnection);

        if (!periods) {
            return null;
        }

        // Parse salary effective month (format: "M,YYYY" e.g., "10,2024")
        const [monthStr, yearStr] = salaryEffectiveMonth.split(',');
        const effectiveMonth = parseInt(monthStr); // 1-12
        const effectiveYear = parseInt(yearStr);

        if (!effectiveMonth || !effectiveYear || isNaN(effectiveMonth) || isNaN(effectiveYear)) {
            return null;
        }

        // Get periods
        const { firstPeriod, secondPeriod } = periods;

        // Check if effective month/year is within financial year (combined first + second period)
        const inFirstPeriod = isMonthYearInPeriodArray(effectiveMonth, effectiveYear, firstPeriod);
        const inSecondPeriod = isMonthYearInPeriodArray(effectiveMonth, effectiveYear, secondPeriod);

        if (!inFirstPeriod && !inSecondPeriod) {
            // Not in any contribution period
            return null;
        }

        // Determine which contribution period the effective month falls into
        let contributionPeriodArray = null;
        let endMonth = null;
        let endYear = null;

        if (inFirstPeriod) {
            contributionPeriodArray = firstPeriod;
            // Get last month/year from array (last two elements)
            endMonth = firstPeriod[firstPeriod.length - 2];
            endYear = firstPeriod[firstPeriod.length - 1];
        } else if (inSecondPeriod) {
            contributionPeriodArray = secondPeriod;
            // Get last month/year from array (last two elements)
            endMonth = secondPeriod[secondPeriod.length - 2];
            endYear = secondPeriod[secondPeriod.length - 1];
        }

        if (!contributionPeriodArray) {
            return null;
        }

        const firstMonthOfPeriod = contributionPeriodArray[0];

        if (effectiveMonth === firstMonthOfPeriod) {
            // Don't apply logic if salary revision happens in the first month of the period
            return null;
        }

        // If we found a valid period, return the end month in "M,YYYY" format
        if (endMonth && endYear) {
            // Return in same format as Salary_Effective_Month: "M,YYYY"
            const endMonthFormatted = `${endMonth},${endYear}`;
            return endMonthFormatted;
        }

        return null;
    } catch (error) {
        console.error('Error in calculateContributionEndMonth:', error);
        return null;
    }
}
// NOTE: NPS calculation functions (calculateNPSDetails, calculateSlabWiseNps) have been moved
// to commonfunctions.js to avoid GraphQL resolver conflicts. Do NOT export them here.
