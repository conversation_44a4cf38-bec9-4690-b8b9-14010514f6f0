// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const knex = require('knex');
const moment = require('moment');
const { ehrTables } = require('../common/tablealias');
const { validateCommonRuleInput } = require('../common/commonfunctions');

// resolver definition
const resolvers = {
    Mutation: {
        // function to add/update tax configuration details
        addUpdateTaxConfig: async (_parent, args, context) => {
            let organizationDbConnection;
            let validationError = {};
            try {
                console.log('Inside addUpdateTaxConfig function');
                const loggedInEmpId = context.logInEmpId;
                const isEditMode = !!args.taxConfigurationId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    args.formId
                );

                if (Object.keys(checkRights).length === 0) {
                    throw '_DB0100';
                }

                // Check appropriate rights based on operation
                if (isEditMode) {
                    if (checkRights.Role_Update !== 1) {
                        throw '_DB0102';
                    }
                } else {
                    if (checkRights.Role_Add !== 1) {
                        throw '_DB0101';
                    }
                }

                // Input validation using validateCommonRuleInput
                const fieldValidations = {};

                // Map fields to validation rules
                if (args.panNo) fieldValidations.panNo = 'IVE0939';
                if (args.tanNo) fieldValidations.tanNo = 'IVE0940';
                if (args.responsibleEmailId) fieldValidations.responsibleEmailId = 'IVE0942';
                if (args.employerEmail) fieldValidations.employerEmail = 'IVE0943';
                if (args.responsibleMobileNumber) fieldValidations.responsibleMobileNumber = 'IVE0944';
                if (args.responsiblePincode) fieldValidations.responsiblePincode = 'IVE0945';
                if (args.employerPincode) fieldValidations.employerPincode = 'IVE0946';
                if (args.employerPhoneNumber) fieldValidations.employerPhoneNumber = 'IVE0947';
                if (args.employerSTDCode) fieldValidations.employerSTDCode = 'IVE0948';
                if (args.responsibleFullName) fieldValidations.responsibleFullName = 'IVE0949';
                if (args.responsibleDesignation) fieldValidations.responsibleDesignation = 'IVE0950';
                if (args.responsiblePAN) fieldValidations.responsiblePAN = 'IVE0951';
                if (args.responsibleStreet1) fieldValidations.responsibleStreet1 = 'IVE0952';
                if (args.responsibleStreet2) fieldValidations.responsibleStreet2 = 'IVE0953';
                if (args.responsibleCityName) fieldValidations.responsibleCityName = 'IVE0954';
                if (args.employerName) fieldValidations.employerName = 'IVE0955';
                if (args.employerBranchDivision) fieldValidations.employerBranchDivision = 'IVE0956';
                if (args.employerAddress1) fieldValidations.employerAddress1 = 'IVE0957';
                if (args.employerAddress2) fieldValidations.employerAddress2 = 'IVE0958';
                if (args.ptNo) fieldValidations.ptNo = 'IVE0959';
                if (args.orgType) fieldValidations.orgType = 'IVE0960';
                if (args.taxRegime) fieldValidations.taxRegime = 'IVE0961';
                if (args.tdsDepositMethod) fieldValidations.tdsDepositMethod = 'IVE0962';
                if (args.tdsPaymentFrequency) fieldValidations.tdsPaymentFrequency = 'IVE0963';
                if (args.cit) fieldValidations.cit = 'IVE0964';
                if (args.tdsAssessmentRange) fieldValidations.tdsAssessmentRange = 'IVE0965';

                validationError = await validateCommonRuleInput(args, fieldValidations);

                // Additional custom validations that can't be handled by common validation
                await validateTaxConfigSpecificRules(args, validationError);

                if (Object.keys(validationError).length > 0) {
                    throw 'IVE0000';
                }

                // Get Field_Force from org_details
                const orgDetails = await organizationDbConnection('org_details')
                    .select('Field_Force')
                    .where('Org_Code', context.orgCode)
                    .first();

                const isFieldForceEnabled = !!(orgDetails && orgDetails.Field_Force);

                // Validate Field Force constraints for add operations
                await validateFieldForceConstraints(organizationDbConnection, args, isFieldForceEnabled, args.taxConfigurationId);


                // Process add/update operation
                const result = await processTaxConfiguration(organizationDbConnection, args, loggedInEmpId, isEditMode);

                // Create system log activities
                const systemLogParam = {
                    organizationDbConnection,
                    loggedInEmpId,
                    action: isEditMode ? 'update' : 'add',
                    tableName: ehrTables.taxConfiguration,
                    uniqueId: result.taxConfigurationId,
                    changedData: JSON.stringify(args),
                    formId: args.formId,
                };
                await commonLib.func.createSystemLogActivities(systemLogParam);

                // return response
                return {
                    errorCode: '',
                    message: isEditMode ? 'Tax configuration updated successfully.' : 'Tax configuration added successfully.',
                    success: true,
                    taxConfigurationId: result.taxConfigurationId
                };
            }
            catch (mainCatchError) {
                console.log('Error in addUpdateTaxConfig function main catch block', mainCatchError);
                if (mainCatchError === 'IVE0000') {
                    const errResult = commonLib.func.getError('', 'IVE0000');
                    throw new UserInputError(errResult.message, { validationError: validationError });
                } else {
                    const errResult = commonLib.func.getError(mainCatchError, 'PST0050');
                    throw new ApolloError(errResult.message, errResult.code);
                }
            }
            finally {
                if (organizationDbConnection) {
                    await organizationDbConnection.destroy();
                }
            }
        }
    }
};

// function to validate tax configuration specific rules that can't be handled by common validation
async function validateTaxConfigSpecificRules(args, validationError) {
    // Validate enum fields
   
    const validAddressChanged = ['Y', 'N'];
    if (args.isResponserAddressChanged && !validAddressChanged.includes(args.isResponserAddressChanged)) {
        validationError['IVE0966'] = commonLib.func.getError('', 'IVE0966').message1;
    }

    if (args.responsibleAddressChanged && !validAddressChanged.includes(args.responsibleAddressChanged)) {
        validationError['IVE0967'] = commonLib.func.getError('', 'IVE0967').message1;
    }

    if (args.isEmployerAddressChanged && !validAddressChanged.includes(args.isEmployerAddressChanged)) {
        validationError['IVE0968'] = commonLib.func.getError('', 'IVE0968').message1;
    }

    const validYesNo = [1, 0];
    if (args.roundOffTds && !validYesNo.includes(args.roundOffTds)) {
        validationError['IVE0969'] = commonLib.func.getError('', 'IVE0969').message1;
    }

    const validJoiningMonthConfigs = ['Standard_Tax_Calculation','No_Tax_Deduction','Deduct_Tax_On_Non_Salary_Components_Only'];
    if (args.joiningMonthTaxConfig && !validJoiningMonthConfigs.includes(args.joiningMonthTaxConfig)) {
        validationError['IVE0970'] = commonLib.func.getError('', 'IVE0970').message1;
    }

}

// function to validate Field Force constraints for tax configuration
async function validateFieldForceConstraints(organizationDbConnection, args, isFieldForceEnabled, currentTaxConfigurationId = null) {
    try {
        if (isFieldForceEnabled) {
            // Field Force: Yes - One tax config per service provider
            if (!args.serviceProviderId) {
                throw 'PST0056'; // Service Provider ID is required when Field Force is enabled
            }

            // Check if tax configuration already exists for this service provider
            const existingConfigQuery = organizationDbConnection(ehrTables.taxConfiguration)
                .where('Service_Provider_Id', args.serviceProviderId);
            if (currentTaxConfigurationId) {
                existingConfigQuery.andWhereNot('Tax_Configuration_Id', currentTaxConfigurationId);
            }
            const existingConfig = await existingConfigQuery.first();

            if (existingConfig) {
                throw 'PST0057'; // Tax configuration already exists for this service provider
            }
        } else {
            // Field Force: No - Only one tax config allowed in total            
            const existingConfigQuery = organizationDbConnection(ehrTables.taxConfiguration)
            .whereNull('Service_Provider_Id');
            if (currentTaxConfigurationId) {
               existingConfigQuery.whereNot('Tax_Configuration_Id', currentTaxConfigurationId);
            }
            const existingConfig = await existingConfigQuery.first();


            if (existingConfig) {
                throw 'PST0058'; // Tax configuration already exists. Only one tax configuration is allowed when Field Force is disabled
            }
        }
    } catch (error) {
        console.log('Error in validateFieldForceConstraints:', error);
        throw error;
    }
}

// function to process tax configuration add/update
async function processTaxConfiguration(organizationDbConnection, args, loggedInEmpId, isEditMode) {
    try {
        return await organizationDbConnection.transaction(async (trx) => {
            const currentTimestamp = moment.utc().format('YYYY-MM-DD HH:mm:ss');

            // Prepare tax configuration data
            const taxConfigData = {
                Service_Provider_Id: args.serviceProviderId || null,
                Org_Type: args.orgType || null,
                Tax_Regime: args.taxRegime || null,
                PAN: args.panNo || null,
                TAN: args.tanNo || null,
                PT_No: args.ptNo || null,
                TDS_Deposit_Method: args.tdsDepositMethod || null,
                TDS_Payment_Frequency: args.tdsPaymentFrequency || null,
                CIT: args.cit || null,
                TDS_Assessment_Range: args.tdsAssessmentRange || null,
                Round_Off_Tds: args.roundOffTds ?? null,
                Form16_Signatory: args.form16SignatoryId || null,
                Joining_Month_Tax_Config: args.joiningMonthTaxConfig || null,
                Tax_Residency_Threshold: args.taxResidencyThreshold ?? null,
                Is_Responser_Address_Changed: args.isResponserAddressChanged || null,
                Responsible_Full_Name: args.responsibleFullName || null,
                Responsible_Designation: args.responsibleDesignation || null,
                Responsible_PAN: args.responsiblePAN || null,
                Responsible_Mobile_Number: args.responsibleMobileNumber || null,
                Responsible_Email_Id: args.responsibleEmailId || null,
                Responsible_Street1: args.responsibleStreet1 || null,
                Responsible_Street2: args.responsibleStreet2 || null,
                Responsible_Pincode: args.responsiblePincode || null,
                Responsible_City_Name: args.responsibleCityName || null,
                Responsible_State_Code: args.responsibleStateCode ?? null,
                Responsible_Address_Changed: args.responsibleAddressChanged || null,
                Employer_Name: args.employerName || null,
                Employer_Branch_Division: args.employerBranchDivision || null,
                Employer_Address1: args.employerAddress1 || null,
                Employer_Address2: args.employerAddress2 || null,
                Employer_State_Code: args.employerStateCode ?? null,
                Employer_Pincode: args.employerPincode || null,
                Employer_Email: args.employerEmail || null,
                Employer_STD_Code: args.employerSTDCode || null,
                Employer_Phone_Number: args.employerPhoneNumber || null,
                Is_Employer_Address_Changed: args.isEmployerAddressChanged || null
            };

            // Set audit fields
            if (isEditMode) {
                taxConfigData.Updated_By = loggedInEmpId;
                taxConfigData.Updated_On = currentTimestamp;
            } else {
                taxConfigData.Added_By = loggedInEmpId;
                taxConfigData.Added_On = currentTimestamp;
            }

            let taxConfigurationId;

            if (isEditMode) {
                // Update existing tax configuration
                await organizationDbConnection(ehrTables.taxConfiguration)
                    .where('Tax_Configuration_Id', args.taxConfigurationId)
                    .update(taxConfigData)
                    .transacting(trx);

                taxConfigurationId = args.taxConfigurationId;
            } else {
                // Insert new tax configuration
                const [insertId] = await organizationDbConnection(ehrTables.taxConfiguration)
                    .insert(taxConfigData)
                    .transacting(trx);

                taxConfigurationId = insertId;
            }

            return { taxConfigurationId };
        });
    } catch (error) {
        console.log('Error in processTaxConfiguration:', error);
        throw error;
    }
}

module.exports.resolvers = resolvers;