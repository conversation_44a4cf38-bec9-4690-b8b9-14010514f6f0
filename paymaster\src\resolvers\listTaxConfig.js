// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');

// resolver definition
const resolvers = {
    Query: {
        // function to list tax configuration details
        listTaxConfig: async (parent, args, context, info) => {
            let organizationDbConnection;
            try {
                console.log('Inside listTaxConfig function');
                const loggedInEmpId = context.logInEmpId;
                const { formId } = args;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    formId
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // get tax configuration data
                const taxConfigData = await getTaxConfig(organizationDbConnection);

                // return response
                return {
                    errorCode: '',
                    message: 'Tax configuration details listed successfully.',
                    success: true,
                    taxConfigDetails: JSON.stringify(taxConfigData)
                };
            }
            catch (mainCatchError) {
                console.log('Error in listTaxConfig function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0049');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally {
                if (organizationDbConnection) {
                    await organizationDbConnection.destroy()
                }
            }
        },

    }
};

// function to get tax configuration details
async function getTaxConfig(organizationDbConnection) {
    try {
        return await organizationDbConnection.transaction(async (trx) => {
            let taxConfigQuery = organizationDbConnection(ehrTables.taxConfiguration + ' as TCF')
                .select(
                    'TCF.Tax_Configuration_Id as taxConfigurationId',
                    'TCF.Service_Provider_Id as serviceProviderId',
                    'TCF.Org_Type as orgType',
                    'TCF.Tax_Regime as taxRegime',
                    'TCF.PAN as panNo',
                    'TCF.TAN as tanNo',
                    'TCF.PT_No as ptNo',
                    'TCF.TDS_Deposit_Method as tdsDepositMethod',
                    'TCF.TDS_Payment_Frequency as tdsPaymentFrequency',
                    'TCF.CIT as cit',
                    'TCF.TDS_Assessment_Range as tdsAssessmentRange',
                    'TCF.Round_Off_Tds as roundOffTds',
                    'TCF.Form16_Signatory as form16SignatoryId',
                    'TCF.Joining_Month_Tax_Config as joiningMonthTaxConfig',
                    'TCF.Tax_Residency_Threshold as taxResidencyThreshold',
                    'TCF.Is_Responser_Address_Changed as isResponserAddressChanged',
                    'TCF.Responsible_Full_Name as responsibleFullName',
                    'TCF.Responsible_Designation as responsibleDesignation',
                    'TCF.Responsible_PAN as responsiblePAN',
                    'TCF.Responsible_Mobile_Number as responsibleMobileNumber',
                    'TCF.Responsible_Email_Id as responsibleEmailId',
                    'TCF.Responsible_Street1 as responsibleStreet1',
                    'TCF.Responsible_Street2 as responsibleStreet2',
                    'TCF.Responsible_Pincode as responsiblePincode',
                    'TCF.Responsible_City_Name as responsibleCityName',
                    'TCF.Responsible_State_Code as responsibleStateCode',
                    'TCF.Responsible_Address_Changed as responsibleAddressChanged',
                    'TCF.Employer_Name as employerName',
                    'TCF.Employer_Branch_Division as employerBranchDivision',
                    'TCF.Employer_Address1 as employerAddress1',
                    'TCF.Employer_Address2 as employerAddress2',
                    'TCF.Employer_State_Code as employerStateCode',
                    'TCF.Employer_Pincode as employerPincode',
                    'TCF.Employer_Email as employerEmail',
                    'TCF.Employer_STD_Code as employerSTDCode',
                    'TCF.Employer_Phone_Number as employerPhoneNumber',
                    'TCF.Is_Employer_Address_Changed as isEmployerAddressChanged',
                    'TCF.Added_On as addedOn',
                    'TCF.Updated_On as updatedOn',
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as updatedBy"),
                    organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as addedBy")
                )
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI', 'EPI.Employee_Id', 'TCF.Updated_By')
                .leftJoin(ehrTables.empPersonalInfo + ' as EPI2', 'EPI2.Employee_Id', 'TCF.Added_By')
                .orderBy('TCF.Tax_Configuration_Id', 'asc')
                .transacting(trx);

            const taxConfigDetails = await taxConfigQuery;

            return taxConfigDetails;
        });
    } catch (error) {
        console.log('Error in getTaxConfig function:', error);
        throw error;
    }
}

module.exports.resolvers = resolvers;

