{"securityGroupIds": ["sg-04bc41f567ae532b6"], "subnetIds": ["subnet-0cbbb2391d86f1d0e", "subnet-0120a712c5936a292"], "dbSecretName": "PROD/UPSHOT/PGACCESS", "region": "eu-west-2", "lambdaRole": "arn:aws:iam::327313496531:role/lambdaFullAccess", "dbPrefix": "upshothr_", "domainName": "upshothr", "customDomainName": "api.upshothr.uk", "firebaseAuthorizer": "arn:aws:lambda:eu-west-2:327313496531:function:ATS-upshothr-firebaseauthorizer", "profileBucket": "s3.images.upshothr.uk", "documentsBucket": "s3.taxdocs.upshothr.uk", "sesTemplatesRegion": "eu-west-2", "sourceEmailAddress": "<EMAIL>", "logoBucket": "s3.logos.upshothr.uk", "webAddress": ".uk", "employeeTaxDetailUrl": "https://{orgCode}.hrapp.co.in/payroll/salary-payslip/get-employee-tax-details", "resourceArnPrefix": "arn:aws:lambda:eu-west-2:327313496531:function:PAYMASTER-upshothr", "processCancelSalaryRevisionsFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-processCancelSalaryRevisionsFunction", "processSalaryImportFunction": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-processSalaryImportFunction", "stateMachineArn": "arn:aws:states:eu-west-2:327313496531:stateMachine:upshothr-bulkPayslipProcessing", "bucketName": "s3.taxdocs.upshothr.uk", "bulkProcessingLambdaArn": "arn:aws:lambda:eu-west-2:327313496531:function:BULKPROCESSING-upshothr-graphql", "initiatePayslipRefreshArn": "arn:aws:lambda:eu-west-2:327313496531:function:upshothr-initiatePayslipRefresh"}