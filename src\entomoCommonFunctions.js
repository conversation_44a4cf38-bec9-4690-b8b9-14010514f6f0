const axios = require('axios');
const moment = require('moment-timezone');
const { ehrTables } = require('../common/tablealias');
const { triggerLambda, getError } = require('./CommonFunctions');
const commonValues = require('./AppConstants');
// Local Testing
// const { decryptKeys } = require('../../ats-be/src/signinresolvers/decryptKeys')

//Function to get the entomo client and secret id
async function getEntomoClientDetails() {
    try {
        const AWS = require('aws-sdk');

        // Create client for secrets manager
        let client = new AWS.SecretsManager({
            region: process.env.region
        });
        // Get secrets from aws secrets manager
        let secretKeys = await client.getSecretValue({ SecretId: process.env.dbSecretName }).promise();
        secretKeys = JSON.parse(secretKeys.SecretString);

        let clientDetails = {
            Client_Id: secretKeys.entomo_clientid,
            Client_Secret: secretKeys.entomo_clientsecret,
            API_Key: secretKeys.entomo_apikey ? secretKeys.entomo_apikey : null,
        };
        return clientDetails;
    }
    catch (error) {
        console.log("Error in the getEntomoClientDetails function main catch block.", error);
        return {};
    }
}

//Function to get the access token using client id,secret and username
async function getEntomoAccessToken(requestUrl, employeeUserName, clientId, clientSecret) {
    try {
        const requestBody = {
            clientId: clientId,
            clientSecret: clientSecret,
            username: employeeUserName //working user name "cebpro6"
        };
        const config = {
            method: 'post',
            url: requestUrl,
            maxBodyLength: Infinity,
            data: requestBody,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        const response = await axios.request(config)

        if (!response.data.accessToken) {
            console.log('Error response in the getEntomoAccessToken function', response);
            throw 'Entomo access token not retrieved in getEntomoAccessToken function.';
        } else {
            let accessTokenResponse = {
                entomoAccessToken: response.data.accessToken
            };
            return accessTokenResponse;
        }
    }
    catch (error) {
        console.log("Error in the getEntomoAccessToken function main catch block.", error);
        return {};
    }
}

//Function to get the entomo user login details using access token
async function getEntomoLoginDetailsFromAccessToken(requestUrl, accessToken) {
    try {
        const requestBody = {
            accessToken: accessToken
        };
        const config = {
            method: 'post',
            url: requestUrl,
            maxBodyLength: Infinity,
            data: requestBody,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        }
        const response = await axios.request(config)

        if (!response.headers) {
            console.log('Error response in the getEntomoLoginDetailsFromAccessToken function', response);
            throw 'Headers are empty in the getEntomoLoginDetailsFromAccessToken function.';
        } else {
            let loginDetails = response.headers;
            if (loginDetails['x-auth-token'] && loginDetails['x-refresh-token'] && loginDetails['x-auth-token-expiry'] && loginDetails['x-refresh-token-expiry']) {
                let loginResponse = {
                    xAuthToken: loginDetails['x-auth-token'],
                    xRefreshToken: loginDetails['x-refresh-token'],
                    xAuthTokenExpiry: loginDetails['x-auth-token-expiry'],
                    xRefreshTokenExpiry: loginDetails['x-refresh-token-expiry']
                };
                return loginResponse;
            } else {
                console.log('Token and expiry time not exist in the getEntomoLoginDetailsFromAccessToken function', response);
                return {};
            }
        }
    }
    catch (error) {
        console.log("Error in the getEntomoLoginDetailsFromAccessToken function main catch block.", error);
        return {};
    }
}


//Function to get the x-auth token using refresh token
async function getAuthTokenUsingRefreshToken(requestUrl, xAuthToken, refreshToken) {
    let accessTokenResponse = {};

    try {
        const config = {
            method: 'get',
            url: requestUrl,
            headers: {
                "X-REFRESH-TOKEN": refreshToken,
                "X-AUTH-TOKEN": xAuthToken
            }
        }
        const response = await axios.request(config)
        if (!response.headers) {
            console.log('Error response in the getAuthTokenUsingRefreshToken function', response);
            //Return empty response in order to get the access token and x-auth-token
            return accessTokenResponse;
        } else {
            let loginDetails = response.headers;
            if (loginDetails['x-auth-token'] && loginDetails['x-refresh-token'] && loginDetails['x-auth-token-expiry'] && loginDetails['x-refresh-token-expiry']) {
                accessTokenResponse = {
                    xAuthToken: loginDetails['x-auth-token'],
                    xRefreshToken: loginDetails['x-refresh-token'],
                    xAuthTokenExpiry: loginDetails['x-auth-token-expiry'],
                    xRefreshTokenExpiry: loginDetails['x-refresh-token-expiry']
                };
            } else {
                console.log('Empty response in the getAuthTokenUsingRefreshToken function', response);
            }
            return accessTokenResponse;
        }
    }
    catch (error) {
        console.log("Error in the getAuthTokenUsingRefreshToken function main catch block.", error);
        //Return empty response in order to get the access token and x-auth-token
        return {};
    }
}

//Function to get the entomo login details from email id
async function getEntomoLoginDetailsFromEmail(employeeUserName, partnerBaseUrl) {
    try {
        let { Client_Id, Client_Secret } = await getEntomoClientDetails();

        if (Client_Id && Client_Secret) {
            let { entomoAccessToken } = await getEntomoAccessToken(partnerBaseUrl + process.env.entomoAccessTokenUrl, employeeUserName, Client_Id, Client_Secret);

            if (entomoAccessToken) {
                let entomoLoginDetails = await getEntomoLoginDetailsFromAccessToken(partnerBaseUrl + process.env.entomoLoginUrl, entomoAccessToken);

                return entomoLoginDetails;
            } else {
                return {};
            }
        } else {
            console.log("Error in getEntomoLoginDetailsFromEmail() function: there is no client id or secret id configured", Client_Id, Client_Secret);
            return {};
        }

    } catch (error) {
        console.log("Error in the getEntomoLoginDetailsFromEmail function main catch block.", error);
        return '';
    }
}

//Function to get the login details based on either the refresh token or email
async function getLoginDetailsUsingRefreshToken(xAuthTokenInput, xRefreshTokenInput, employeeUserName, partnerBaseUrl) {
    try {
        let loginResponse = {};
        //If refresh token exist
        if (xAuthTokenInput && xRefreshTokenInput) {
            //Get the x-auth token
            let { xAuthToken, xRefreshToken, xAuthTokenExpiry, xRefreshTokenExpiry } = await getAuthTokenUsingRefreshToken(partnerBaseUrl + process.env.entomoRefreshTokenUrl, xAuthTokenInput, xRefreshTokenInput);
            //If x-auth-token exist
            if (xAuthToken && xRefreshToken && xAuthTokenExpiry && xRefreshTokenExpiry) {
                loginResponse.xAuthToken = xAuthToken;
                loginResponse.xRefreshToken = xRefreshToken;
                loginResponse.xAuthTokenExpiry = xAuthTokenExpiry;
                loginResponse.xRefreshTokenExpiry = xRefreshTokenExpiry;
            } else {
                console.log("Empty response retrieved from getLoginDetailsUsingRefreshToken function", loginResponse);
            }
        } else {
            console.log("Empty xauthtoken and xrefreshtoken in the getLoginDetailsUsingRefreshToken function", xAuthTokenInput, xRefreshTokenInput)
        }
        if (loginResponse && Object.keys(loginResponse).length > 0) {
            return loginResponse;
        } else {
            if (employeeUserName) {
                loginResponse = await getEntomoLoginDetailsFromEmail(employeeUserName, partnerBaseUrl);
                loginResponse.isLoginApiCalled = 1;
            }
            return loginResponse;
        }
    } catch (error) {
        console.log("Error in the getLoginDetailsUsingRefreshToken function main catch block.", error);
        return {};
    }
}

//Function to logout from entomo
async function logoutUserFromEntomo(requestUrl, xAuthToken, partnerBaseUrl) {
    try {
        const config = {
            method: 'get',
            url: partnerBaseUrl + requestUrl,
            headers: {
                "x-auth-token": xAuthToken
            }
        }
        await axios.request(config)
            .then(() => {
                return 1;
            })
            .catch(error => {
                //If the users is already logged out
                if (error.response.status && error.response.status == 403) {
                    return 1;
                } else {
                    return 0;
                }
            });
    }
    catch (error) {
        console.log("Error in the logoutUserFromEntomo function main catch block.", error);
        throw error;
    }
}

//Function to ungzip inputs
async function ungzipData(input) {
    try {
        const { ungzip } = require('node-gzip');

        let decodedData = Buffer.from(input.split('.')[1], 'base64');

        let ungzipResult = ungzip(decodedData)
            .then(data => {
                let ungzipData = JSON.parse(data.toString());
                let expiryTimeStamp = ungzipData.exp ? ungzipData.exp : '';
                let parseSubString = ungzipData.sub ? JSON.parse(ungzipData.sub) : '';
                let userName = parseSubString.username ? parseSubString.username : '';
                let emailAddress = parseSubString.user.employee.email ? parseSubString.user.employee.email : '';
                let decompressTokenResponse = {
                    expiryTimeStamp: expiryTimeStamp,
                    userName: userName,
                    emailAddress: emailAddress
                };
                return decompressTokenResponse;
            }).catch(err => {
                console.log('Error in the ungzip .catch block', err);
                return {};
            });

        return ungzipResult;
    } catch (error) {
        console.log('Error in the ungzipData function main catch block.', error);
        return {};
    }
}

async function extractRiseAppTokenDetails(token, orgCode) {
    try {
        console.log('Inside extractRiseAppTokenDetails function', token, orgCode);
        const jwt = require('jsonwebtoken');

        // First, try to decode the token without verification to inspect its structure
        let decodedHeader;
        try {
            decodedHeader = jwt.decode(token, { complete: true });
            console.log("decodedHeader:", decodedHeader);
            console.log("Token header:", decodedHeader?.header);
        } catch (decodeError) {
            console.log('Error decoding token header:', decodeError);
            return {};
        }

        // Check if this is a Firebase token or other type that should be handled differently
        if (decodedHeader?.header?.alg && decodedHeader.header.alg !== 'HS256') {
            console.log(`Token uses algorithm ${decodedHeader.header.alg}, not HS256. Attempting to decode without verification.`);
            // For non-HS256 tokens (like Firebase RS256 tokens), just decode without verification
            // This is acceptable for extracting user information if we're not doing security-critical operations
            const decoded = jwt.decode(token);
            console.log("decoded (without verification)...", decoded);
            return decoded;
        }

        // JWT verification options for HS256 tokens
        const verifyOptions = {
            algorithms: ['HS256'],
            ignoreExpiration: true
        };

        const hardcodedSecrets = {
            'fieldforce': 'fieldforce-super-secure-jwt-secret-for-testing-2024',
            'ansr': 'ansr-super-secure-jwt-secret-for-testing-2024',
            'default': 'default-super-secure-jwt-secret-for-testing-2024'
        };

        const jwtSecret = hardcodedSecrets[orgCode] || hardcodedSecrets['default'];

        // Verify and decode the access token for HS256 tokens
        const decoded = jwt.verify(token, jwtSecret, verifyOptions);
        console.log("decoded (with verification)...", decoded);
        return decoded;
    } catch (error) {
        console.log('Error in the extractRiseAppTokenDetails function main catch block.', error);

        // As a fallback, try to decode without verification to at least extract user info
        try {
            const jwt = require('jsonwebtoken');
            const fallbackDecoded = jwt.decode(token);
            console.log("Fallback decoded (no verification)...", fallbackDecoded);
            return fallbackDecoded || {};
        } catch (fallbackError) {
            console.log('Fallback decode also failed:', fallbackError);
            return {};
        }
    }
}

async function getRiseAppEmployeeIdFromUserName(token, organizationDbConnection, orgCode) {
    try {
        // variable declarations
        var errResult = {};

        let decodedTokenInfo = await extractRiseAppTokenDetails(token, orgCode);
        // check decodedTokenInfo is null or not
        if (decodedTokenInfo && decodedTokenInfo.sub) {
            // get firebase userid
            let empEmail = decodedTokenInfo.sub;
            console.log("empEmail...",empEmail);
            // get the employeeid from emp_user table using login userid        
            return (
                organizationDbConnection(ehrTables.empJob)
                .select('Employee_Id')
                .where({
                    Emp_Email: empEmail,
                    Emp_Status: 'Active'
                })
                .first()
                .then(data => {
                    console.log("data...",data);
                    // check employeeId exist or not
                    if (data && data.Employee_Id) {
                        // return response to hanlder
                        return data.Employee_Id;
                    } else {
                        console.log('Employee Id does not exist.');
                        throw "SIB0131";
                    }
                })
                //return the success result to user
                .then(function (result) {
                    return result;
                })
                .catch(function (insCatchError) {
                    console.log('Error in getEntomoEmployeeIdFromUserName() .catch block', insCatchError);
                    errResult = getError(insCatchError, '_UH0001');
                    throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message }));
                })
            )
        }
        else {
            console.log('Error in getEmployeeByToken() function else block', decodedTokenInfo);
            errResult = getError('', 'ERE0124');
            throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message }));
        }
    } catch (mainCatchError) {
        console.log('Error in getEntomoEmployeeIdFromUserName() function main catch block', mainCatchError);
        errResult = getError(mainCatchError, '_UH0001');
        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message }));
    }

}

//Get employee id in hrapp for the entomo user
async function getEntomoEmployeeIdFromUserName(token, organizationDbConnection) {
    try {
        // variable declarations
        var errResult = {};

        let decodedTokenInfo = await ungzipData(token);
        // check decodedTokenInfo is null or not
        if (decodedTokenInfo && decodedTokenInfo.userName) {
            // get firebase userid
            let loginUserId = decodedTokenInfo.userName;
            // get the employeeid from emp_user table using login userid        
            return (
                organizationDbConnection
                    .select('emp_user.Employee_Id')
                    .from('emp_user')
                    .innerJoin('emp_job', 'emp_user.Employee_Id', 'emp_job.Employee_Id')
                    .where('Firebase_Uid', loginUserId)
                    .where('emp_job.Emp_Status', 'Active')
                    .then(data => {
                        // check employeeId exist or not
                        if (data[0] && data[0].Employee_Id) {
                            // return response to hanlder
                            return data[0].Employee_Id;
                        } else {
                            console.log('Employee Id does not exist.');
                            throw "SIB0131";
                        }
                    })
                    //return the success result to user
                    .then(function (result) {
                        return result;
                    })
                    .catch(function (insCatchError) {
                        console.log('Error in getEntomoEmployeeIdFromUserName() .catch block', insCatchError);
                        errResult = getError(insCatchError, '_UH0001');
                        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message }));
                    })
            )
        }
        else {
            console.log('Error in getEmployeeByToken() function else block', decodedTokenInfo);
            errResult = getError('', 'ERE0124');
            throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message }));
        }
    } catch (mainCatchError) {
        console.log('Error in getEntomoEmployeeIdFromUserName() function main catch block', mainCatchError);
        errResult = getError(mainCatchError, '_UH0001');
        throw new Error(JSON.stringify({ errorCode: errResult.code, message: errResult.message }));
    }
};

async function getPartnerBaseUrl(orgCode, stageName) {
    try {
        let partnerBaseUrl = '';
        console.log("Inside getPartnerBaseUrl function orgCode", orgCode, stageName);
        switch (orgCode) {
            case 'cebprouat':
            case 'cebuanaprouat':
            case 'govph':
                partnerBaseUrl = 'https://apjuat.entomo.co/';
                break;
            case 'cebpro':
                partnerBaseUrl = 'http://cebuanapro.entomo.co/';
                break;
            case 'hmcgroupuat':
                partnerBaseUrl = 'https://adeera-uat.entomo.co/';
                break;
            case 'demoindonesia':
                partnerBaseUrl = 'https://showpeople.entomo.co/';
            case 'thailand':
                    partnerBaseUrl = 'https://icetesting.entomo.co/';   
            case 'demophilippines':
                partnerBaseUrl = 'https://showpeople.entomo.co/';  
                break;
            default:
                if (stageName === 'prod') {
                    partnerBaseUrl = 'https://' + orgCode + '.entomo.co/';
                } else {
                    partnerBaseUrl = 'https://apjuat.entomo.co/';
                }
                break;
        }
        return partnerBaseUrl;
    } catch (error) {
        console.log('Error in the getPartnerBaseUrl function main catch block.', error);
        return '';
    }
}

/**
 * Function to form the Entomo API Url based on the given entity type
 * @param {string} apiUrl - The base URL of the Entomo API
 * @param {string} entityType - The type of entity for which the API Url is required
 * @returns {string} The formed Entomo API Url
 */
function formEntomoAPIUrl(apiUrl, entityType) {
    try {
        const postAPIUrls =
        {
            'designation': '/designations',
            'employee type': '/employment-types',
            'employee grade': '/job-grades',
            'organization group': '/org-categories',
            'business unit': '/employee-custom-entities',
            'employee': '/employees',
            'department': '/org-units',
            'organization type': '/org-unit-types'
        };

        // Special handling for resignemployee - needs different base URL path
        if (entityType.toLowerCase() === 'resignemployee') {
            // Transform URL from /v1/hris to /v1/webAdmin/manageEmployee/resignEmployees
            // e.g., https://apjuat.entomo.co/epms/v1/hris -> https://apjuat.entomo.co/epms/v1/webAdmin/manageEmployee/resignEmployees
            const webAdminUrl = apiUrl.replace('/v1/hris', '/v1/webAdmin');
            return webAdminUrl + '/manageEmployee/resignEmployees';
        }

        return apiUrl + postAPIUrls[entityType.toLowerCase()];
    }
    catch (error) {
        console.log('Error in the formEntomoAPIUrl function main catch block.', error);
        //In the Parent function, error message will be handled
        throw new Error(error);
    }
}

/**
 * This function is used to sync the data to entomo.
 * @param {object} context - contains the login employee id and auth token
 * @param {object} entityObj - contains the entity type, id, action(add/update/delete) and data
 * @param {string} apiUrl - entomo base url
 * @returns {object} error message if any
 */
async function syncEntomoData(context, entityObj, apiUrl, isUpdate = false) {
    try {
        console.log('Inside syncEntomoData function...');

        if (!apiUrl || !apiUrl.length) {
            console.log('Entomo URL not found');
            return { errors: ['Entomo URL not found'] };
        }

        const methods = { 'add': 'POST', 'update': 'PUT', 'delete': 'DELETE' };
        const fullUrl = formEntomoAPIUrl(apiUrl, entityObj.Entity_Type);
        let bodyData = null;

        // Handle resignemployee sync type
        if (entityObj.Entity_Type?.toLowerCase() === 'resignemployee') {
            bodyData = entityObj.Form_Data;

            if (!bodyData || !bodyData.employeeCode) {
                console.log('No employee code to resign in entomo', bodyData);
                return { errors: ['Invalid data - No employee code provided'] };
            }

            // Convert HRApp employee code to Entomo employee ID
            const employeeUrl = formEntomoAPIUrl(apiUrl, 'Employee');
            const employeeObj = {
                Form_Data: {
                    Old_Code: bodyData.employeeCode
                }
            };
            let { id, message } = await getIdFromCode(context, employeeObj, employeeUrl);

            if (!id) {
                console.log('Error in resigning employee - Entomo employee Id not found', message);
                return { errors: [message || 'Entomo employee Id not found'] };
            }

            // Form the final request body with employeeIds array and forceResign
            bodyData = {
                employeeIds: [id],
                forceResign: entityObj.Form_Data.forceResign
            };
        } else if (entityObj.Entity_Type?.toLowerCase() !== 'employee') {
            bodyData = entityObj.Form_Data;

            if (!bodyData || Object.keys(bodyData).length === 0) {
                console.log('No data to be synced to entomo', bodyData);
                return { errors: ['Invalid data'] };
            }

            //If Entity type is Department, Get the Organization Type
            if (entityObj.Entity_Type.toLowerCase() === 'department' && entityObj.Action.toLowerCase() !== 'delete') {
                let orgTypeObj = {
                    Form_Data: {
                        Old_Code: entityObj.Form_Data.type
                    }
                }
                let orgTypeUrl = formEntomoAPIUrl(apiUrl, 'Organization Type');
                let { id, message } = await getIdFromCode(context, orgTypeObj, orgTypeUrl);
                if (id) {
                    bodyData.type = id;
                } else {
                    console.log('Error in syncing entomo data', 'Id not found');
                    return { errors: [message] };
                }
                if (bodyData.parentId) {
                    let departmentObj = {
                        Form_Data: {
                            Old_Code: bodyData.parentId
                        }
                    }
                    let departmentUrl = formEntomoAPIUrl(apiUrl, 'Department');
                    let { id, message } = await getIdFromCode(context, departmentObj, departmentUrl);
                    if (id) {
                        bodyData.parentId = id;
                    }
                    else {
                        console.log('Error in syncing entomo data', 'Id not found');
                        return { errors: [message] };
                    }
                }
            }

            //If the method is update or delete, get the id with the code
            if (entityObj.Action.toLowerCase() === 'update' || entityObj.Action.toLowerCase() === 'delete') {
                let { id, message } = await getIdFromCode(context, entityObj, fullUrl);
                if (id) {
                    bodyData.id = id;
                } else {
                    console.log('Error in syncing entomo data', 'Id not found');
                    return { errors: [message] };
                }
            }
        } else {
            let formEmployeeEntomoAPIBodyResult = entityObj.Form_Data;

            const idConfigurations = [
                {
                    entityType: 'Employee Grade',
                    codeKey: 'jobGradeId',
                    urlSuffix: 'Employee Grade',
                    required: true,
                    errorMsg: 'Grade Id not found'
                },
                {
                    entityType: 'Designation',
                    codeKey: 'designationId',
                    urlSuffix: 'Designation',
                    required: true,
                    errorMsg: 'Designation Id not found'
                },
                {
                    entityType: 'Employee Type',
                    codeKey: 'employmentTypeId',
                    urlSuffix: 'Employee Type',
                    required: true,
                    errorMsg: 'Employee Type Id not found'
                },
                {
                    entityType: 'Business Unit',
                    codeKey: 'businessUnitId',
                    urlSuffix: 'Business Unit',
                    required: false,
                    errorMsg: 'Business Unit Id not found / Not Mapped'
                },
                {
                    entityType: 'Employee',
                    codeKey: 'primarySupervisorId',
                    urlSuffix: 'Employee',
                    required: false,
                    errorMsg: 'Primary Supervisor Id not found / Not Mapped'
                },
                {
                    entityType: 'Department',
                    codeKey: 'organizationUnitId',
                    urlSuffix: 'Department',
                    required: true,
                    errorMsg: 'Department Id not found'
                }
            ];

            // Generate promises for all ID lookups
            const idPromises = idConfigurations.map(config => {
                const code = formEmployeeEntomoAPIBodyResult[config.codeKey];
                const url = formEntomoAPIUrl(apiUrl, config.urlSuffix);
                return getIdFromCode(
                    context,
                    {
                        Entity_Type: config.entityType,
                        Form_Data: { Old_Code: code }
                    },
                    url
                );
            });

            const results = await Promise.all(idPromises);

            // Process results and handle errors
            for (let i = 0; i < idConfigurations.length; i++) {
                const { codeKey, required, errorMsg } = idConfigurations[i];
                const result = results[i];
                console.log('result', result);
                console.log(idConfigurations[i]);

                if (result?.id === 0) {
                    console.log('Error in syncing entomo data', errorMsg);

                    if (required) {
                        return { errors: [result.message || errorMsg] };
                    }

                    formEmployeeEntomoAPIBodyResult[codeKey] = null;
                } else {
                    formEmployeeEntomoAPIBodyResult[codeKey] = result?.id ?? null;
                }
            }

            bodyData = formEmployeeEntomoAPIBodyResult;

            console.log('formEmployeeEntomoAPIBodyResult', bodyData);

            //If the method is update or delete, get the id with the code
            if (entityObj.Action.toLowerCase() === 'update' || entityObj.Action.toLowerCase() === 'delete') {
                entityObj.Form_Data = formEmployeeEntomoAPIBodyResult;
                entityObj.Form_Data.Old_Code = entityObj.Form_Data.Old_Code || entityObj.Form_Data.code;
                let { id, message } = await getIdFromCode(context, entityObj, fullUrl);
                if (id) {
                    bodyData.id = id;
                } else {
                    console.log('Error in syncing entomo data', 'Id not found');
                    return { errors: [message] };
                }
            }

        }

        const options = {
            url: fullUrl + (bodyData.id ? '/' + bodyData.id : ''),
            method: methods[entityObj.Action?.toLowerCase()],
            headers: {
                'Content-Type': 'application/json',
                'X-API-KEY': context.API_Key
            },
            data: bodyData
        };

        console.log('Options', options);
        const response = await axios.request(options);

        console.log('Response', response);

        if (response.status === 200 || response.status === 204) {
            return null;
        } else {
            console.log('Error in syncing entomo data', response.data || response.message);
            return response.data || response.message;
        }
    } catch (error) {
        console.log('Error in syncing entomo data catch', error);
        return error.response?.data || error.message;
    }
}

/**
 * This function is used to get the id of the entity from the code.
 * @param {object} context - contains the login employee id and auth token
 * @param {object} entityObj - contains the entity type, id, action(add/update/delete) and data
 * @param {string} fullUrl - entomo base url
 * @returns {number} id of the entity
 */
async function getIdFromCode(context, entityObj, fullUrl) {
    try {
        console.log("Inside getIdFromCode function", entityObj);

        if (!context || !context.API_Key) {
            console.log('Error in getIdFromCode function', 'Context or API Key is null');
            return { id: 0, message: 'Context or API Key is null' };
        }

        if (!entityObj || !entityObj.Form_Data || !entityObj.Form_Data.Old_Code || !fullUrl) {
            console.log('Error in getIdFromCode function', 'Entity object or code or fullUrl is null');
            return { id: 0, message: 'Entity object or code or fullUrl is null' };
        }

        const options = {
            url: fullUrl + '/code/' + entityObj.Form_Data.Old_Code,
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-API-KEY': context.API_Key
            },
        };

        const response = await axios.request(options);
        if (response.status === 200 && response.data && response.data.id) {
            return { id: response.data.id, message: '' };
        } else {
            console.log('Error in syncing entomo data', response);
            return { id: 0, message: response.data || response.message };
        }

    } catch (error) {
        console.log("Error in getIdFromCode function", error);
        return { id: 0, message: error.response?.data || error.message };
    }
}


/**
 * This function is used to sync the data to entomo, if the partnerId is 'entomo' and Sync_Type is 'PUSH'
 * @param {object} organizationDbConnection - connection to the organization database
 * @param {object} context - contains the login employee id and auth token
 * @param {object} entityObj - contains the entity type, id, action(add/update/delete) and data
 * @returns {boolean} true if the data is synced successfully, otherwise false
 */
async function syncEntomoEntityData(organizationDbConnection, context, entityObj) {
    try {
        console.log('Inside syncEntomoEntityData function...');

        if (!context || !context.partnerid || context.partnerid.toLowerCase() !== 'entomo') return true;

        //Get the API key
        let { API_Key, Api_Url, Sync_Type } = await getEntomoAPIDetails(organizationDbConnection);
        
        //If Sync_Type doesn't exists or it is not 'PUSH', return true
        if (!Sync_Type || Sync_Type.toLowerCase() !== 'push') return true;

        const entomoObj = {
            Entity_Type: entityObj.Entity_Type,
            Entity_Id: entityObj.Entity_Id,
            Action: entityObj.Action,
            Form_Data: await formEntomoAPIBody(entityObj, organizationDbConnection),
            Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
            Added_By: context.Employee_Id,
            Integration_Type: 'Entomo'
        };

        // If the Sync_Type is 'PUSH' and Api_Url is null, Add the entomoObj to entomoSyncStatus table
        if (!Api_Url || !Api_Url.trim().length) {
            entomoObj.Status = 'Open';
            entomoObj.Failure_Reason = JSON.stringify({error: 'Entomo Base Url is null'});
            entomoObj.Integration_Type = 'Entomo';
            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(entomoObj);
            return true;
        }


        // If the Sync_Type is 'PUSH' and API_Key is null, Add the entomoObj to entomoSyncStatus table
        if (!API_Key) {
            entomoObj.Status = 'Open';
            entomoObj.Failure_Reason = JSON.stringify({error: 'API_Key is null'});
            entomoObj.Integration_Type = 'Entomo';
            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(entomoObj);
            return true;
        };

        context.API_Key = API_Key.trim();

        // Handle update or delete actions
        if (['update', 'delete'].includes(entityObj.Action.toLowerCase())) {

            //Check for failed records for the same entity and id
            const existingData = await organizationDbConnection(ehrTables.externalAPISyncStatus)
                .select('Entity_Type', 'Entity_Id', 'Status', 'Failure_Reason')
                .where({
                    Entity_Type: entityObj.Entity_Type,
                    Entity_Id: entityObj.Entity_Id,
                    Status: 'Failed',
                    Integration_Type: 'Entomo'
                })
                .first();

            if (existingData) {
                entomoObj.Status = 'Open';
                await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(entomoObj);
            } else {
                await syncAndInsert(organizationDbConnection, entomoObj, entityObj, Api_Url, context);
            }
        } else {
            await syncAndInsert(organizationDbConnection, entomoObj, entityObj, Api_Url, context);
        }

        return true;
    } catch (err) {
        console.log('Error in syncEntomoEntityData', err);
        return false;
    }
}

/**
 * This function is used to form the employee data in the format of Entomo's API body
 * @param {object} organizationDbConnection - connection to the organization database
 * @param {object} entityObj - contains the entity type, id, action(add/update/delete) and data
 * @returns {object} employee data in the format of Entomo's API body
 */
async function formEmployeeEntomoAPIBody(organizationDbConnection, entityObj, oldCode) {
    try {
        const employeeId = entityObj.Entity_Id

        let entomoFormedEmployee = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EP')
            .select('EP.Emp_First_Name as firstName', 'EP.Emp_Last_Name as lastName', 'EJ.Emp_Email as email', 'EJ.Emp_Email as username', 'EP.DOB', 'EDL.Driving_License_No',
                'EJ.User_Defined_EmpId as code', 'EJ.Date_Of_Join as joiningDate', 'EJ.Designation_Id as designationId', 'EJ.EmpType_Id', 'DEP.Department_Code', "EP.Photo_Path",
                'DES.Designation_Code', 'ET.Employee_Type_Code', 'JG.Grade_Code', 'EJ.Manager_Id', 'BU.Business_Unit_Code', 'EPJ.User_Defined_EmpId as primarySupervisorId',
                organizationDbConnection.raw("CONCAT_WS(' ',EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as 'displayName'"))
            .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EP.Employee_Id')
            .innerJoin(ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
            .innerJoin(ehrTables.empGrade + ' as JG', 'JG.Grade_Id', 'EJ.Grade_Id')
            .innerJoin(ehrTables.employeeType + ' as ET', 'ET.EmpType_Id', 'EJ.EmpType_Id')
            .innerJoin(ehrTables.department + " as DEP", "DEP.Department_Id", "EJ.Department_Id")
            .leftJoin(ehrTables.businessUnit + ' as BU', 'BU.Business_Unit_Id', 'EJ.Business_Unit_Id')
            .leftJoin(ehrTables.empDrivinglicense + ' as EDL', 'EDL.Employee_Id', 'EJ.Employee_Id')
            .leftJoin(ehrTables.empJob + ' as EPJ', 'EJ.Manager_Id', 'EPJ.Employee_Id')
            .where('EJ.Employee_Id', employeeId)
            .first()

        let entomoEmployee = {
            "displayName": {
                "en": entomoFormedEmployee.displayName
            },
            "email": entomoFormedEmployee.email,
            "username": entomoFormedEmployee.email,
            "firstName": entomoFormedEmployee.firstName,
            "lastName": entomoFormedEmployee.lastName,
            "licenseNumber": entomoFormedEmployee.Driving_License_No,
            "code": entomoFormedEmployee.code,
            "joiningDate": entomoFormedEmployee.joiningDate,
            "organizationUnitId": entomoFormedEmployee.Department_Code,
            "jobGradeId": entomoFormedEmployee.Grade_Code,
            "designationId": entomoFormedEmployee.Designation_Code,
            "employmentTypeId": entomoFormedEmployee.Employee_Type_Code,
            "businessUnitId": entomoFormedEmployee.Business_Unit_Code,
            "jobCategoryId": null,
            "positionId": null,
            "payBasisId": null,
            "nationalityGroupId": null,
            "staffCategoryId": null,
            "primarySupervisorId": entomoFormedEmployee.primarySupervisorId,
            "dateOfBirth": entomoFormedEmployee.DOB
        }

        //TODO: Getting 500 error from Entomo, Need to confirm
        // if (entomoFormedEmployee && entomoFormedEmployee.Photo_Path) {

        //     const AWS = require('aws-sdk')
        //     // Create object for s3 bucket
        //     const s3 = new AWS.S3({ region: process.env.region });

        //     //TODO: Replace with s3 bucket name
        //     const bucket = 's3.hrapp-dev-public-asset';
        //     // Set URL expired time
        //     const signedUrlExpireSeconds = 60 * 60;

        //     const fileName = "hrapp_upload/" + "cebuanaprouat" + "_tmp/images/" + entomoFormedEmployee.Photo_Path;
        //     // Call function headObject to check files exists or not in s3 bucket. Pass bucket name and file name as input
        //     await s3.headObject({ Bucket: bucket, Key: fileName }).promise();

        //     // Call function getSignedUrl and pass getObject/putObject function to getpresigned url
        //     var url = s3.getSignedUrl('getObject', { Bucket: bucket, Key: fileName, Expires: signedUrlExpireSeconds });
        //     entomoEmployee["profileImage"] = {
        //         "extension": ".jpeg",
        //         "height": 0,
        //         "image": url,
        //         "width": 0
        //     }
        // }

        //If old code exists, add it to the entomo employee
        if (oldCode) {
            entomoEmployee.Old_Code = oldCode;
        }

        return entomoEmployee
    }
    catch (err) {
        console.log('Error in formEmployeeEntomoAPIBody', err);
        return false;
    }
}

async function syncFailedEntityData(organizationDbConnection, context, failedRecord, Api_Url) {
    try {
        console.log('Inside syncFailedEntityData function...');

        //Parse the JSON data
        failedRecord.Form_Data = JSON.parse(failedRecord.Form_Data);

        await syncAndInsert(organizationDbConnection, failedRecord, failedRecord, Api_Url, context, true);

        return true

    } catch (err) {
        console.log('Error in syncFailedEntityData', err);
        return false;
    }
}

/**
 * Syncs the data to entomo and inserts the result into the organization database
 * @param {object} organizationDbConnection - connection to the organization database
 * @param {object} entomoObj - contains the entity type, id, action(add/update/delete), data and status
 * @param {object} entityObj - contains the entity type, id, action(add/update/delete) and data
 * @param {string} apiUrl - the base url of the entomo API
 * @param {object} context - contains the login employee id and auth token
 * @returns {boolean} true if the data is synced and inserted successfully, otherwise false
 */
async function syncAndInsert(organizationDbConnection, entomoObj, entityObj, apiUrl, context, isUpdate = false) {
    try {
        let copiedEntomoObj = JSON.parse(JSON.stringify(entomoObj));
        let errorInSync = await syncEntomoData(context, entomoObj, apiUrl, isUpdate);
        if (typeof errorInSync === 'string') {
            errorInSync = {
                "error": errorInSync
            }
        };
        copiedEntomoObj.Status = errorInSync ? 'Failed' : 'Success';
        copiedEntomoObj.Failure_Reason = errorInSync ? errorInSync : null;

        if (isUpdate) {
            await organizationDbConnection(ehrTables.externalAPISyncStatus)
                .where('Entomo_Sync_Id', copiedEntomoObj.Entomo_Sync_Id)
                .where('Integration_Type', 'Entomo')
                .update({
                    Status: copiedEntomoObj.Status,
                    Failure_Reason: JSON.stringify(copiedEntomoObj.Failure_Reason),
                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Updated_By: context.Employee_Id
                });
        } else {
            await organizationDbConnection(ehrTables.externalAPISyncStatus).insert(copiedEntomoObj);
        }

        return true;
    }
    catch (err) {
        console.log('Error in syncAndInsert', err);
        return false;
    }
}
/**
 * Forms the body of the entomo API request based on the entity type and action
 * @param {object} entityObj - contains the entity type, id, action(add/update/delete) and data
 * @returns {object} the body data for the entomo API request
 */
async function formEntomoAPIBody(entityObj, organizationDbConnection) {
    try {
        let bodyData = {}
        if (entityObj.Entity_Type.toLowerCase() === 'designation' || entityObj.Entity_Type.toLowerCase() === 'employee type' ||
            entityObj.Entity_Type.toLowerCase() === 'organization group' || entityObj.Entity_Type.toLowerCase() === 'employee grade' ||
            entityObj.Entity_Type.toLowerCase() === 'business unit'
        ) {

            if (entityObj.Action.toLowerCase() === 'add') {
                bodyData = {
                    "code": entityObj.Form_Data.Code,
                    "description": {
                        "en": entityObj.Form_Data.Description
                    },
                    "level": entityObj.Form_Data.Level ? entityObj.Form_Data.Level : 1,
                    "name": {
                        "en": entityObj.Form_Data.Name
                    }
                }
            } else if (entityObj.Action.toLowerCase() === 'update') {
                bodyData = {
                    "code": entityObj.Form_Data.Code,
                    "description": {
                        "en": entityObj.Form_Data.Description
                    },
                    "level": entityObj.Form_Data.Level ? entityObj.Form_Data.Level : 1,
                    "name": {
                        "en": entityObj.Form_Data.Name
                    },
                    "id": entityObj.Form_Data.Id,
                    "Old_Code": entityObj.Form_Data.Old_Code
                }
            } else {
                bodyData = {
                    "id": entityObj.Form_Data.Id,
                    "Old_Code": entityObj.Form_Data.Old_Code
                }
            }
        } else if (entityObj.Entity_Type.toLowerCase() === 'employee') {
            let oldUserDefinedEmpId = entityObj.Form_Data?.Old_Code;
            bodyData = await formEmployeeEntomoAPIBody(organizationDbConnection, entityObj, oldUserDefinedEmpId)
        }
        else if (entityObj.Entity_Type.toLowerCase() === 'department') {
            if (entityObj.Action.toLowerCase() === 'delete') {
                bodyData = {
                    "id": entityObj.Form_Data.Id,
                    "Old_Code": entityObj.Form_Data.Old_Code
                }
            } else {
                bodyData = {
                    "code": entityObj.Form_Data.Code,
                    "costCenterAdministratorId": null,
                    "costCenterManagerId": null,
                    "description": {
                        "en": entityObj.Form_Data.Description
                    },
                    "headOfUnitId": null,
                    "hrSpocId": null,
                    "leaveAdministratorId": null,
                    "name": {
                        "en": entityObj.Form_Data.Name
                    },
                    "parentId": entityObj.Form_Data.parentId ? entityObj.Form_Data.parentId : null,
                    "type": entityObj.Form_Data.type
                }
                //If Old Code exits include it in the body data
                if (entityObj.Form_Data.Old_Code) {
                    bodyData.Old_Code = entityObj.Form_Data.Old_Code;
                }
            }
        }
        else if (entityObj.Entity_Type.toLowerCase() === 'resignemployee') {
            // For resignemployee, we need to get the User_Defined_EmpId (code) to convert to Entomo ID later
            const employeeId = entityObj.Entity_Id;
            const employeeData = await organizationDbConnection(ehrTables.empJob)
                .select('User_Defined_EmpId')
                .where('Employee_Id', employeeId)
                .first();

            bodyData = {
                "employeeCode": employeeData?.User_Defined_EmpId,
                "forceResign": true
            };
        }

        //External entities
        if (entityObj.Entity_Type.toLowerCase() === 'business unit') {
            bodyData.type = "BUSINESS_UNIT"
        }

        return bodyData
    }
    catch (err) {
        console.log('Error in formEntomoAPIBody', err);
        return {}
    }
}

/**
 * Retrieves the entomo API details (API URL and API key) from the external api credentials table
 * @param {object} organizationDbConnection - the knex connection object for the organization database
 * @returns {object} an object containing the API URL and API key
 */
async function getEntomoAPIDetails(organizationDbConnection){
    try {
        let entomoCredentials = await organizationDbConnection(ehrTables.externalApiCredentials)
            .select('Api_Url', 'Credential1', 'Sync_Type')
            .where('Integration_Type', 'Entomo')
            .first();

        if(entomoCredentials && entomoCredentials.Credential1 && entomoCredentials.Sync_Type?.toLowerCase() === 'push'){
            //Decrypt the credentials
            let payload = { 
                isBase64SecretKey: 1,
                isEncryptedInputEncoded: 1,
                textToDecrypt: entomoCredentials.Credential1,
                decryptionAlgorithm: commonValues.decryptionAlgorithm,
                decryptionSecretKeyName: commonValues.decryptionSecretKeyName,
                allowEmptySecretKey: 1
            };
            // Added for local testing
            // const event = {
            //     body: JSON.stringify(payload)
            // }
            // let decryptCredential = await decryptKeys(event);
            // decryptCredential = JSON.parse(decryptCredential.body);
            const decryptCredential = await triggerLambda('decrypt-keys', 'RequestResponse', payload);
            if(decryptCredential?.decryptedString){
                entomoCredentials.API_Key = decryptCredential.decryptedString;
            }
        }else{
            return {
                Api_Url: null,
                API_Key: null,
                Sync_Type: entomoCredentials?.Sync_Type
            }
        }

        return entomoCredentials;
    } catch (error) {
        console.log('Error in getEntomoAPIDetails', error);
        return {
            Api_Url: null,
            API_Key: null,
            Sync_Type: null
        };
    }
}
module.exports = {
    getEntomoClientDetails,
    getEntomoAccessToken,
    getEntomoLoginDetailsFromAccessToken,
    getAuthTokenUsingRefreshToken,
    getEntomoLoginDetailsFromEmail,
    getLoginDetailsUsingRefreshToken,
    logoutUserFromEntomo,
    ungzipData,
    getEntomoEmployeeIdFromUserName,
    getRiseAppEmployeeIdFromUserName,
    extractRiseAppTokenDetails,
    getPartnerBaseUrl,
    syncEntomoEntityData,
    formEntomoAPIBody,
    syncAndInsert,
    syncEntomoData,
    syncFailedEntityData,
    getEntomoAPIDetails
};