const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const { formId } = require('../common/appconstants');

const triggerSalaryImport = async (parent, args, context) => {
  let organizationDbConnection;
  
  try {

    // Get user info from context
    const {
      logInEmpId: loginEmployeeId,
      orgCode
    } = context;

    // Initialize database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    const resolvedImportType = args.importType || 'Revision';
    const isAddImport = resolvedImportType === 'Add Salary';
    const records = isAddImport ? args.salaryAddRecords : args.salaryRevisionRecords;

    // Validate input
    if (!records || !Array.isArray(records) || records.length === 0) {
      throw new Error('records array is required and cannot be empty');
    }

    // Validate each record structure
    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      
      // Common required fields
      if (!record.Employee_Id) {
        throw new Error(`Record ${i + 1}: Employee_Id is required`);
      }
      if (!record.Employee_Name || record.Employee_Name.trim() === '') {
        throw new Error(`Record ${i + 1}: Employee_Name is required`);
      }
      if (!record.Salary_Template) {
        throw new Error(`Record ${i + 1}: Salary_Template (Template_Id) is required`);
      }

      if (isAddImport) {
        if (!record.Annual_Ctc || record.Annual_Ctc <= 0) {
          throw new Error(`Record ${i + 1}: Annual_Ctc must be a positive number`);
        }
      } else {
        if (!record.Previous_Annual_Ctc || record.Previous_Annual_Ctc <= 0) {
          throw new Error(`Record ${i + 1}: Previous_Annual_Ctc must be a positive number`);
        }
        if (!record.Revise_By || !['Amount', 'Percentage'].includes(record.Revise_By)) {
          throw new Error(`Record ${i + 1}: Revise_By must be either 'Amount' or 'Percentage'`);
        }
      }
    }

    // Check access rights
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      isAddImport ? formId.salaryDetailsId : formId.salaryRevisionId
    );

    if (!checkRights || Object.keys(checkRights).length === 0 || checkRights.Role_Add !== 1) {
      throw '_DB0101';
    }

    // Create import tracking record in salary_import table
    const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');
    const [salaryImportId] = await organizationDbConnection('salary_import')
      .insert({
        Import_Type: resolvedImportType,
        Import_Status: 'In Progress',
        Added_On: currentTimestamp,
        Added_By: loginEmployeeId
      });

    // Prepare Step Function input
    const stepFunctionInput = {
      salaryImportId: salaryImportId,
      orgCode: orgCode,
      loginEmployeeId: loginEmployeeId,
      importType: resolvedImportType,
      salaryRevisionRecords: isAddImport ? [] : records,
      salaryAddRecords: isAddImport ? records : [],
      totalRecords: records.length,
      batchSize: 5, // Process 5 records in parallel
      timestamp: moment().utc().toISOString()
    };

    const stepFnResult = await commonLib.stepFunctions.triggerStepFunction(
      process.env.processSalaryImportFunction,
      'processSalaryImportFunction',
      '',
      stepFunctionInput
    );

    try {
      await commonLib.func.createSystemLogActivities({
        userIp: context.userIp || '',
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Bulk salary ${isAddImport ? 'add' : 'revision'} import initiated: ${records.length} records queued for processing (Import ID: ${salaryImportId})`
      });
    } catch (logError) {
      console.warn('Salary import started but system log failed:', logError);
    }


    return {
      errorCode: '',
      message: `Salary ${isAddImport ? 'add' : 'revision'} import triggered successfully. Processing will continue asynchronously.`,
      salaryImportId: salaryImportId,
      executionArn: stepFnResult?.executionArn || null,
      totalRecords: records.length,
      status: 'In Progress'
    };

  } catch (error) {
    console.error('Error in triggerSalaryImport:', error);
    
    // Handle specific error types
    let errorCode = 'SALARY_IMPORT_ERROR';
    let errorMessage = error.message || 'An error occurred while initiating bulk salary import';
    
    if (error.code === 'ValidationException') {
      errorCode = 'VALIDATION_ERROR';
    } else if (error.code === 'InvalidParameterValueException') {
      errorCode = 'INVALID_PARAMETER';
    } else if (error.code === 'StateMachineDoesNotExist') {
      errorCode = 'STEP_FUNCTION_NOT_FOUND';
    }

    throw new ApolloError(errorMessage, errorCode);
    
  } finally {
    // Clean up database connections
    if (organizationDbConnection) {
      organizationDbConnection.destroy();
    }
  }
};

module.exports = {
  triggerSalaryImport
};
