//Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
//Require knex to make DB connection
const knex = require('knex');
const { ehrTables } = require('../../../common/tableAlias');
//Require apollo server to return error message
const { ApolloError } = require('apollo-server-lambda');

module.exports.listVehicleDetails = async function (parent, args, context, info) {
    console.log('Inside listVehicleDetails function');
    let organizationDbConnection;
    let errResult;
    try {
        const { formId } = args;
        
        // get the organization database connection
        organizationDbConnection = knex(context.connection.OrganizationDb);

        // Check access rights
        const checkRights = await commonLib.func.checkEmployeeAccessRights(
            organizationDbConnection,
            context.Employee_Id,
            null,
            '',
            'UI',
            false,
            formId
        );

        if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
            throw '_DB0100';
        }

        // get vehicle details
        const getVehicleData = await getVehicleDetails(organizationDbConnection);

        // return response
        return {
            errorCode: '',
            message: 'Vehicle details listed successfully.',
            success: true,
            getVehicleDetails: Object.keys(getVehicleData).length ? getVehicleData: []
        };
    }
    catch (mainCatchError) {
        console.log('Error in listVehicleDetails function main catch block', mainCatchError);
        errResult = commonLib.func.getError(mainCatchError, 'SRE0002');
        throw new ApolloError(errResult.message, errResult.code);
    }
    finally {
        if(organizationDbConnection) {
            await organizationDbConnection.destroy();
        }
    }
};

// function to get vehicle details
async function getVehicleDetails(organizationDbConnection) {
    try {
        return await organizationDbConnection(ehrTables.vehicleDetails + " as VD")
            .select(
                'VD.Vehicle_Id as vehicleId',
                'VD.Vehicle_Name as vehicleName',
                'VD.Record_Mileage_Using as recordMileageUsing',
                'VD.Vehicle_Type as vehicleType',
                'VD.Engine_Capacity_CC as engineCapacityCC',
                'VD.Record_Status as recordStatus',
                'VD.Added_On as addedOn',
                'VD.Updated_On as updatedOn',
                organizationDbConnection.raw("CONCAT_WS(' ',EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as updatedBy"),
                organizationDbConnection.raw("CONCAT_WS(' ',EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as addedBy")
            )
            .leftJoin(ehrTables.empPersonalInfo + " as EPI", "EPI.Employee_Id", "VD.Updated_By")
            .leftJoin(ehrTables.empPersonalInfo + " as EPI2", "EPI2.Employee_Id", "VD.Added_By")
            .orderBy('Vehicle_Name', 'asc');

    } catch (error) {
        console.log('Error in getVehicleDetails function:', error);
        throw error;
    }
}