/**
 * Excel file generation utilities for salary import using ExcelJS
 */

const { Workbook } = require('exceljs');
const { ehrTables } = require('./tablealias');

/**
 * Get active salary templates from database
 * @param {Object} organizationDbConnection - Database connection
 * @returns {Array} - Array of active salary templates
 */
async function getSalaryTemplates(organizationDbConnection) {
  if (!organizationDbConnection) {
    console.warn('No database connection provided, returning empty template list');
    return [];
  }

  try {
    const templates = await organizationDbConnection(ehrTables.salaryTemplate)
      .select('Template_Id', 'Template_Name')
      .where('Template_Status', 'Active')
      .orderBy('Template_Name', 'asc');

    return templates;
  } catch (error) {
    console.error('Error fetching salary templates:', error);
    return [];
  }
}

/**
 * Convert template ID to template name
 * @param {Object} organizationDbConnection - Database connection
 * @param {number|string} templateId - Template ID to convert
 * @param {Array} templateList - List of templates (for caching)
 * @returns {string} - Template name or original value if not found
 */
async function getTemplateName(organizationDbConnection, templateId, templateList = []) {
  if (!templateId) return '';

  // If templateId is already a string (template name), return as-is
  if (typeof templateId === 'string' && isNaN(Number(templateId))) {
    return templateId;
  }

  // Try to find in provided template list first (for performance)
  if (templateList.length > 0) {
    const template = templateList.find(t => t.Template_Id == templateId);
    if (template) return template.Template_Name;
  }

  // If not found in list and we have database connection, query database
  if (organizationDbConnection) {
    try {
      const template = await organizationDbConnection(ehrTables.salaryTemplate)
        .select('Template_Name')
        .where('Template_Id', templateId)
        .where('Template_Status', 'Active')
        .first();

      return template ? template.Template_Name : templateId.toString();
    } catch (error) {
      console.error('Error fetching template name:', error);
      return templateId.toString();
    }
  }

  return templateId.toString();
}

async function getEmployeeId(organizationDbConnection, employeeId) {
  if (!employeeId) return '';
  if (!organizationDbConnection) {
   console.log('No database connection provided for employee ID lookup');
   return employeeId.toString();
}

  try {
    const employee = await organizationDbConnection(ehrTables.empJob)
      .select('User_Defined_EmpId')
      .where('Employee_Id', employeeId)
      .first();
      return employee?.User_Defined_EmpId ?? employeeId.toString();
    } catch (error) {
      console.error('Error fetching User_Defined_EmpId:', error);
      return employeeId.toString();
    }
  }

/**
 * Add list validation to worksheet column
 * @param {Object} worksheet - ExcelJS worksheet
 * @param {Object} validationWorksheet - Hidden validation worksheet
 * @param {string} headerText - Header text to find column
 * @param {Array} validationList - List of validation values
 * @param {string} columnLetter - Column letter (A, B, C, etc.)
 */
function addListValidation(worksheet, validationWorksheet, headerText, validationList, columnLetter) {
  if (!validationList || validationList.length === 0) {
    return;
  }

  // Add validation data to hidden sheet
  validationList.forEach((item, index) => {
    validationWorksheet.getCell(`${columnLetter}${index + 1}`).value = item;
  });

  // Apply validation to first 1000 rows of the main worksheet
  const validationRange = `${columnLetter}2:${columnLetter}1001`;
  const formulaRange = `ValidationSheet!$${columnLetter}$1:$${columnLetter}$${validationList.length}`;

  worksheet.dataValidations.add(validationRange, {
    type: 'list',
    allowBlank: true,
    formulae: [formulaRange],
    showErrorMessage: true,
    errorStyle: 'error',
    errorTitle: 'Invalid Selection',
    error: `Please select a valid ${headerText} from the dropdown list.`
  });
}

/**
 * Generate original Excel file with all records using ExcelJS
 * @param {Array} records - Array of salary import records
 * @param {number} salaryImportId - Salary Import ID for unique filename
 * @param {Object} organizationDbConnection - Database connection (optional)
 * @returns {Object} - { fileName, buffer }
 */
async function generateOriginalFile(records, salaryImportId, organizationDbConnection = null, importType = 'Revision') {
  try {
    // Create workbook and worksheet
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Salary Import');

    const resolvedImportType = importType || 'Revision';
    const isAddImport = resolvedImportType === 'Add Salary';

    // Define headers as per import type
    const reportHeaders = isAddImport
      ? [
          { header: 'Employee Id', key: 'User_Defined_EmpId', width: 15 },
          { header: 'Employee Name', key: 'Employee_Name', width: 25 },
          { header: 'Salary Template', key: 'Salary_Template', width: 20 },
          { header: 'Annual CTC', key: 'Annual_Ctc', width: 18 }
        ]
      : [
          { header: 'Employee Id', key: 'User_Defined_EmpId', width: 15 },
          { header: 'Employee Name', key: 'Employee_Name', width: 25 },
          { header: 'Salary Template', key: 'Salary_Template', width: 20 },
          { header: 'Annual CTC', key: 'Previous_Annual_Ctc', width: 18 },
          { header: 'Revise By', key: 'Revise_By', width: 15 },
          { header: 'Percentage / Amount', key: 'Amount_Or_Percentage', width: 20 }
        ];

    // Set worksheet columns
    worksheet.columns = reportHeaders;

    // Get salary templates for both ID to name conversion and dropdown validation
    const salaryTemplates = await getSalaryTemplates(organizationDbConnection);
    const DANGEROUS_EXCEL_PREFIX = /^[=+\-@]/;
    const sanitizeForExcel = (value) => {
      if (value === null || value === undefined) return '';
      const str = String(value);
      return DANGEROUS_EXCEL_PREFIX.test(str) ? `'${str}` : str;
    };
    const reportData = await Promise.all(records.map(async record => {
      const base = {
        User_Defined_EmpId: sanitizeForExcel(await getEmployeeId(organizationDbConnection, record.Employee_Id)),
        Employee_Name: sanitizeForExcel(record.Employee_Name || ''),
        Salary_Template: sanitizeForExcel(await getTemplateName(organizationDbConnection, record.Salary_Template, salaryTemplates))
      };
      if (isAddImport) {
        return {
          ...base,
          Annual_Ctc: sanitizeForExcel(record.Annual_Ctc ?? '')
        };
      }
      return {
        ...base,
        Previous_Annual_Ctc: sanitizeForExcel(record.Previous_Annual_Ctc ?? ''),
        Revise_By: sanitizeForExcel(record.Revise_By || ''),
        Amount_Or_Percentage: sanitizeForExcel(record.Amount_Or_Percentage ?? '')
      };
    }));


    worksheet.addRows(reportData);

    // Create hidden validation worksheet for dropdown data
    const validationWorksheet = workbook.addWorksheet('ValidationSheet');
    validationWorksheet.state = 'hidden';

    // Use already fetched salary templates for dropdown validation
    const templateNames = salaryTemplates.map(template => template.Template_Name);

    // Add dropdown validations
    if (templateNames.length > 0) {
      addListValidation(worksheet, validationWorksheet, 'Salary Template', templateNames, 'C');
    }
    if (!isAddImport) {
      // Static validation data for Revise By
      const reviseByOptions = ['Percentage', 'Amount'];
      addListValidation(worksheet, validationWorksheet, 'Revise By', reviseByOptions, 'E');
    }

    // Apply header styling
    const headerRowNumber = 1;
    const headerColor = "92CDDC"; // Light blue color

    worksheet.getRow(headerRowNumber).eachCell((cell) => {
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: headerColor },
      };
      cell.alignment = { vertical: "middle", horizontal: "center" };
      cell.font = {
        name: "Calibri",
        size: 11,
        bold: true,
      };
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });

    // Auto-width columns based on content
    worksheet.columns.forEach(column => {
      if (column.header === 'Employee Name') {
        column.width = 25;
      } else if (column.header === 'Employee Id') {
        column.width = 15;
      } else if (column.header === 'Salary Template') {
        column.width = 20;
      } else if (column.header === 'Annual CTC') {
        column.width = 18;
      } else if (column.header === 'Revise By') {
        column.width = 15;
      } else if (column.header === 'Percentage / Amount') {
        column.width = 20;
      }
    });

    // Freeze header row
    worksheet.views = [
      {
        state: "frozen",
        xSplit: 0,
        ySplit: 1,
      },
    ];

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `salary_import_original_${salaryImportId}_${timestamp}.xlsx`;

    // Create buffer for S3 upload
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      fileName,
      buffer
    };

  } catch (error) {
    console.error('Error generating original Excel file:', error);
    throw error;
  }
}

/**
 * Generate error Excel file with only failed records using ExcelJS
 * @param {Array} errorRecords - Array of failed salary import records
 * @param {number} salaryImportId - Salary Import ID for unique filename
 * @param {Object} organizationDbConnection - Database connection (optional)
 * @returns {Object} - { fileName, buffer }
 */
async function generateErrorFile(errorRecords, salaryImportId, organizationDbConnection = null, importType = 'Revision') {
  try {
    const DANGEROUS_EXCEL_PREFIX = /^[=+\-@]/;
    const sanitizeForExcel = (value) => {
      if (value === null || value === undefined) return '';
      const str = String(value);
      return DANGEROUS_EXCEL_PREFIX.test(str) ? `'${str}` : str;
    };

    const resolvedImportType = importType || 'Revision';
    const isAddImport = resolvedImportType === 'Add Salary';

    // Create workbook and worksheet
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Import Errors');

    // Define headers as per your existing specification + 2 extra error columns
    const reportHeaders = isAddImport
      ? [
          { header: 'Employee Id', key: 'User_Defined_EmpId', width: 15 },
          { header: 'Employee Name', key: 'Employee_Name', width: 25 },
          { header: 'Salary Template', key: 'Salary_Template', width: 20 },
          { header: 'Annual CTC', key: 'Annual_Ctc', width: 18 },
          { header: 'Error Code', key: 'errorCode', width: 15 },
          { header: 'Error Message', key: 'errorMessage', width: 50 }
        ]
      : [
          { header: 'Employee Id', key: 'User_Defined_EmpId', width: 15 },
          { header: 'Employee Name', key: 'Employee_Name', width: 25 },
          { header: 'Salary Template', key: 'Salary_Template', width: 20 },
          { header: 'Annual CTC', key: 'Previous_Annual_Ctc', width: 18 },
          { header: 'Revise By', key: 'Revise_By', width: 15 },
          { header: 'Percentage / Amount', key: 'Amount_Or_Percentage', width: 20 },
          { header: 'Error Code', key: 'errorCode', width: 15 },
          { header: 'Error Message', key: 'errorMessage', width: 50 }
        ];

    // Set worksheet columns
    worksheet.columns = reportHeaders;

    // Get salary templates for both ID to name conversion and dropdown validation
    const salaryTemplates = await getSalaryTemplates(organizationDbConnection);

    const reportData = await Promise.all(errorRecords.map(async record => {
      const base = {
        User_Defined_EmpId: sanitizeForExcel(record.User_Defined_EmpId || record.Employee_Id || ''),
        Employee_Name: sanitizeForExcel(record.Employee_Name || ''),
        Salary_Template: sanitizeForExcel(await getTemplateName(organizationDbConnection, record.Salary_Template, salaryTemplates)),
        errorCode: sanitizeForExcel(record.errorCode || ''),
        errorMessage: sanitizeForExcel(record.errorMessage || '')
      };
      if (isAddImport) {
        return {
          ...base,
          Annual_Ctc: sanitizeForExcel(record.Annual_Ctc ?? '')
        };
      }
      return {
        ...base,
        Previous_Annual_Ctc: sanitizeForExcel(record.Previous_Annual_Ctc ?? ''),
        Revise_By: sanitizeForExcel(record.Revise_By || ''),
        Amount_Or_Percentage: sanitizeForExcel(record.Amount_Or_Percentage ?? '')
      };
    }));


    worksheet.addRows(reportData);

    // Create hidden validation worksheet for dropdown data
    const validationWorksheet = workbook.addWorksheet('ValidationSheet');
    validationWorksheet.state = 'hidden';

    // Use already fetched salary templates for dropdown validation
    const templateNames = salaryTemplates.map(template => template.Template_Name);

    // Add dropdown validations
    if (templateNames.length > 0) {
      addListValidation(worksheet, validationWorksheet, 'Salary Template', templateNames, 'C');
    }
    if (!isAddImport) {
      // Static validation data for Revise By
      const reviseByOptions = ['Percentage', 'Amount'];
      addListValidation(worksheet, validationWorksheet, 'Revise By', reviseByOptions, 'E');
    }

    // Apply header styling
    const headerRowNumber = 1;
    const headerColor = "92CDDC"; // Light blue color

    worksheet.getRow(headerRowNumber).eachCell((cell) => {
      cell.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: headerColor },
      };
      cell.alignment = { vertical: "middle", horizontal: "center" };
      cell.font = {
        name: "Calibri",
        size: 11,
        bold: true,
      };
      cell.border = {
        top: { style: "thin" },
        left: { style: "thin" },
        bottom: { style: "thin" },
        right: { style: "thin" },
      };
    });

    // Auto-width columns with special handling for Error Message
    worksheet.columns.forEach(column => {
      if (column.header === 'Employee Name') {
        column.width = 25;
      } else if (column.header === 'Employee Id') {
        column.width = 15;
      } else if (column.header === 'Salary Template') {
        column.width = 20;
      } else if (column.header === 'Annual CTC') {
        column.width = 18;
      } else if (column.header === 'Revise By') {
        column.width = 15;
      } else if (column.header === 'Percentage / Amount') {
        column.width = 20;
      } else if (column.header === 'Error Code') {
        column.width = 15;
      } else if (column.header === 'Error Message') {
        column.width = 40; // 40 chars wide as specified
      }
    });

    // Freeze header row
    worksheet.views = [
      {
        state: "frozen",
        xSplit: 0,
        ySplit: 1,
      },
    ];

    // Generate unique filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `salary_import_errors_${salaryImportId}_${timestamp}.xlsx`;

    // Create buffer for S3 upload
    const buffer = await workbook.xlsx.writeBuffer();

    return {
      fileName,
      buffer
    };

  } catch (error) {
    console.error('Error generating error Excel file:', error);
    throw error;
  }
}

module.exports = {
  generateOriginalFile,
  generateErrorFile,
  getSalaryTemplates,
  getTemplateName,
  addListValidation
};
